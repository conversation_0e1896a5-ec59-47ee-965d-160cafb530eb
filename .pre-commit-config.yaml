# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
default_language_version:
    python: python3
repos:
-   repo: local
    hooks:
    -   id: add-license-headers
        name: add-license-headers
        entry: nerfstudio/scripts/licensing/license_headers.sh
        language: script
        files: '.*'
        pass_filenames: false
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
-   repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.6.2
    hooks:
    -   id: ruff
        types_or: [ python, pyi, jupyter ]
        args: [ --fix ]
    -   id: ruff-format
        types_or: [ python, pyi, jupyter ]
