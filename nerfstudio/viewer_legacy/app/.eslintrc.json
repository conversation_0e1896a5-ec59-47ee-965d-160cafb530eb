{"env": {"browser": true, "es2021": true}, "extends": ["react-app", "eslint:recommended", "plugin:react/recommended", "airbnb", "prettier", "plugin:import/typescript"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["react", "@typescript-eslint", "unused-imports"], "rules": {"arrow-body-style": "off", "camelcase": "off", "import/prefer-default-export": "off", "no-alert": "off", "no-console": "off", "prefer-destructuring": "off", "react/destructuring-assignment": "off", "react/prop-types": 0, "react/jsx-filename-extension": [2, {"extensions": [".js", ".jsx", ".ts", ".tsx"]}], "unused-imports/no-unused-imports-ts": 2, "import/extensions": ["error", "ignorePackages", {"js": "never", "jsx": "never", "ts": "never", "tsx": "never"}]}}