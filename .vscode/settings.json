{
  "files.watcherExclude": {
    "**/__pycache__/": true,
    "**/.ipynb_checkpoints": true,
    "**/*.ipynb": true,
    "**/old*": true,
    "**/runs*": true,
    "**/temp*": true
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "prettier.configPath": ".prettierrc.js",
  "typescript.suggestionActions.enabled": false,
  "javascript.suggestionActions.enabled": false,
  "[python]": {
    "editor.defaultFormatter": "charliermarsh.ruff",
    "editor.codeActionsOnSave": {
      "source.organizeImports": "explicit",
      "source.fixAll": "explicit"
    }
  },
  "editor.formatOnSave": true,
  "editor.rulers": [120],
  "python.envFile": "${workspaceFolder}/.env",
  "python.formatting.provider": "none",
  "python.linting.pylintEnabled": false,
  "python.linting.flake8Enabled": false,
  "python.linting.enabled": true,
  "python.testing.unittestEnabled": false,
  "python.testing.pytestEnabled": true,
  "python.linting.ignorePatterns": [
    "**/site-packages/**/*.py",
    ".vscode/*.py",
    "external/**/*.py"
  ],
  "esbonio.sphinx.confDir": "",
  // eslint extension options
  "javascript.validate.enable": false,
  "eslint.enable": true,
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "C_Cpp.errorSquiggles": "Disabled",
  "files.associations": {
    "array": "cpp",
    "bitset": "cpp",
    "string_view": "cpp",
    "initializer_list": "cpp",
    "utility": "cpp",
    "__hash_table": "cpp",
    "__split_buffer": "cpp",
    "deque": "cpp",
    "iterator": "cpp",
    "queue": "cpp",
    "stack": "cpp",
    "string": "cpp",
    "unordered_map": "cpp",
    "vector": "cpp",
    "atomic": "cpp",
    "*.tcc": "cpp",
    "cctype": "cpp",
    "chrono": "cpp",
    "clocale": "cpp",
    "cmath": "cpp",
    "codecvt": "cpp",
    "condition_variable": "cpp",
    "cstdarg": "cpp",
    "cstddef": "cpp",
    "cstdint": "cpp",
    "cstdio": "cpp",
    "cstdlib": "cpp",
    "cstring": "cpp",
    "ctime": "cpp",
    "cwchar": "cpp",
    "cwctype": "cpp",
    "exception": "cpp",
    "algorithm": "cpp",
    "filesystem": "cpp",
    "functional": "cpp",
    "memory": "cpp",
    "memory_resource": "cpp",
    "optional": "cpp",
    "ratio": "cpp",
    "system_error": "cpp",
    "tuple": "cpp",
    "type_traits": "cpp",
    "fstream": "cpp",
    "iomanip": "cpp",
    "iosfwd": "cpp",
    "iostream": "cpp",
    "istream": "cpp",
    "limits": "cpp",
    "mutex": "cpp",
    "new": "cpp",
    "ostream": "cpp",
    "sstream": "cpp",
    "stdexcept": "cpp",
    "streambuf": "cpp",
    "thread": "cpp",
    "typeinfo": "cpp",
    "__nullptr": "cpp",
    "__config": "cpp",
    "__locale": "cpp",
    "__bit_reference": "cpp",
    "ios": "cpp",
    "__atomic": "cpp",
    "__node_handle": "cpp"
  },
  "python.analysis.typeCheckingMode": "basic",
  "python.analysis.diagnosticMode": "workspace",
  "eslint.packageManager": "yarn",
}
