name: Core Tests.

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
      - name: Set up Python 3.8.13
        uses: actions/setup-python@v4
        with:
          python-version: '3.8.13'
      - uses: actions/cache@v2
        with:
          path: ${{ env.pythonLocation }}
          key: ${{ env.pythonLocation }}-${{ hashFiles('pyproject.toml') }}
      - name: Install dependencies
        run: |
          pip install uv
          uv pip install --system --upgrade -e .[dev]
      - name: Run license checks
        run: |
          ./nerfstudio/scripts/licensing/license_headers.sh --check
      - name: Check notebook cell metadata
        run: |
          python ./nerfstudio/scripts/docs/add_nb_tags.py --check
      - name: Run Ruff Linter
        run: |
          ruff check docs/ nerfstudio/ tests/ --output-format=github
      - name: Run Ruff Formatter
        run: |
          ruff format docs/ nerfstudio/ tests/ --diff
      - name: Run Pyright
        run: |
          pyright
      - name: Test with pytest
        run: |
          pytest
