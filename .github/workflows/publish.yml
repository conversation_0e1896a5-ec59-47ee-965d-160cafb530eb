# This workflows will upload a Python Package using twine when a release is created
# For more information see: https://help.github.com/en/actions/language-and-framework-guides/using-python-with-github-actions#publishing-to-package-registries

name: Upload Python Package

on:
  release:
    types: [created]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v1
        with:
          python-version: '3.8'
      - name: Install dependencies
        run: |
          python -m pip install build twine
      - name: Strip unsupported tags in README
        run: |
          sed -i '/<!-- pypi-strip -->/,/<!-- \/pypi-strip -->/d' README.md
      - name: Build and publish
        env:
          PYPI_TOKEN: ${{ secrets.PYPI_TOKEN }}
        run: |
          python -m build
          twine upload --username __token__ --password $PYPI_TOKEN dist/*
