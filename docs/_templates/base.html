<!doctype html>
<html class="no-js" {% if language is not none %} lang="{{ language }}" {% endif %}>

<head>
    {%- block site_meta -%}
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <meta name="color-scheme" content="light dark">

    {%- if metatags %}{{ metatags }}{% endif -%}

    {%- block linktags %}
    {%- if hasdoc('about') -%}
    <link rel="author" title="{{ _('About these documents') }}" href="{{ pathto('about') }}" />
    {%- endif -%}
    {%- if hasdoc('genindex') -%}
    <link rel="index" title="{{ _('Index') }}" href="{{ pathto('genindex') }}" />
    {%- endif -%}
    {%- if hasdoc('search') -%}
    <link rel="search" title="{{ _('Search') }}" href="{{ pathto('search') }}" />
    {%- endif -%}
    {%- if hasdoc('copyright') -%}
    <link rel="copyright" title="{{ _('Copyright') }}" href="{{ pathto('copyright') }}" />
    {%- endif -%}
    {%- if next -%}
    <link rel="next" title="{{ next.title|striptags|e }}" href="{{ next.link|e }}" />
    {%- endif -%}
    {%- if prev -%}
    <link rel="prev" title="{{ prev.title|striptags|e }}" href="{{ prev.link|e }}" />
    {%- endif -%}
    {#- rel="canonical" (set by html_baseurl) -#}
    {%- if pageurl %}
    <link rel="canonical" href="{{ pageurl|e }}" />
    {%- endif %}
    {%- endblock linktags %}

    {# Favicon #}
    {%- if favicon_url -%}
    <link rel="shortcut icon" href="{{ favicon_url }}" />
    {%- endif -%}

    {#- Generator banner -#}
    <meta name="generator" content="sphinx-{{ sphinx_version }}, furo {{ furo_version }}" />

    {%- endblock site_meta -%}

    {#- Site title -#}
    {%- block htmltitle -%}
    {% if not docstitle %}
    <title>{{ title|striptags|e }}</title>
    {% elif pagename == master_doc %}
    <title>{{ docstitle|striptags|e }}</title>
    {% else %}
    <title>{{ title|striptags|e }} - {{ docstitle|striptags|e }}</title>
    {% endif %}
    {%- endblock -%}

    {%- block styles -%}

    {# Custom stylesheets #}
    {%- block regular_styles -%}
    {%- for css in css_files -%}
    {% if css|attr("filename") -%}
    {{ css_tag(css) }}
    {%- else -%}
    <link rel="stylesheet" href="{{ pathto(css, 1)|e }}" type="text/css" />
    {%- endif %}
    {% endfor -%}
    {%- endblock regular_styles -%}

    {#- Theme-related stylesheets -#}
    {%- block theme_styles %}
    {% include "partials/_head_css_variables.html" with context %}
    {%- endblock -%}

    {%- block extra_styles %}
    {%- endblock -%}

    {%- endblock styles -%}

    {#- Custom front matter #}
    {%- block extrahead -%}{%- endblock -%}

    {# Custom JS #}
    {%- block regular_scripts -%}
    {% for path in script_files -%}
    {{ js_tag(path) }}
    {% endfor -%}
    {%- endblock regular_scripts -%}

    {# Theme-related JavaScript code #}
    {%- block theme_scripts -%}
    {%- endblock -%}

</head>

<body>
    {% block body %}
    <script>
        document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    {% endblock %}

    {%- block scripts -%}
    {%- endblock scripts -%}
</body>

</html>