version: 5
environments:
  default:
    channels:
    - url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/
    - url: https://conda.anaconda.org/nvidia/
    - url: https://conda.anaconda.org/conda-forge/
    - url: https://conda.anaconda.org/pytorch/
    indexes:
    - https://pypi.org/simple
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_kmp_llvm.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/alsa-lib-1.2.11-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aom-3.9.0-hac33072_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/attr-2.5.1-h166bdaf_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-auth-0.7.18-he0b1f16_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-cal-0.6.11-heb1d5e4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-common-0.9.15-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-compression-0.2.18-hce8ee76_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-event-stream-0.4.2-h01f5eca_8.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-http-0.8.1-hdb68c23_10.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-io-0.14.7-hbfbeace_6.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-mqtt-0.10.4-h50844eb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-s3-0.5.7-h6be9164_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-sdkutils-0.1.15-hce8ee76_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-checksums-0.1.18-hce8ee76_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-crt-cpp-0.26.8-h2150271_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-sdk-cpp-1.11.267-hddb5a97_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/blas-1.0-mkl.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-python-1.1.0-py310hc6cd4ac_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-hd590300_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/c-ares-1.28.1-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ca-certificates-2024.2.2-hbcca054_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cairo-1.18.0-h3faef2a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ceres-solver-2.2.0-h30ec75d_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2024.2.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.3.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/colmap-3.9.1-cpuhe398016_3.conda
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-11.8.0-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-cccl-11.8.89-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-command-line-tools-11.8.0-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-compiler-11.8.0-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-cudart-11.8.89-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-cudart-dev-11.8.89-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-cuobjdump-11.8.86-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-cupti-11.8.87-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-cuxxfilt-11.8.86-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-demo-suite-11.8.86-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-documentation-11.8.86-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-driver-dev-11.8.89-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-gdb-11.8.86-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-libraries-11.8.0-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-libraries-dev-11.8.0-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-memcheck-11.8.86-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-nsight-11.8.86-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-nsight-compute-11.8.0-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-nvcc-11.8.89-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-nvdisasm-11.8.86-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-nvml-dev-11.8.86-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-nvprof-11.8.87-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-nvprune-11.8.86-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-nvrtc-11.8.89-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-nvrtc-dev-11.8.89-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-nvtx-11.8.86-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-nvvp-11.8.87-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-profiler-api-11.8.86-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-runtime-11.8.0-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-sanitizer-api-11.8.86-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-toolkit-11.8.0-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-tools-11.8.0-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-visual-tools-11.8.0-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/dav1d-1.2.1-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.13.6-h5008d03_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/eigen-3.4.0-h00ab1b0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/expat-2.6.2-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ffmpeg-7.0.0-gpl_hdd1146e_100.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/filelock-3.14.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/flann-1.9.2-h2b5ea80_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.14.2-h14ed4e7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/freeimage-3.18.0-h4b96d29_20.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.12.1-h267a509_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fribidi-1.0.10-h36c2ea0_0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/gds-tools-********-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-0.22.5-h59595ed_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-tools-0.22.5-h59595ed_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gflags-2.2.2-he1b5a44_1004.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/glew-2.1.0-h9c3ff4c_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/glib-2.80.0-hf2295e7_6.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/glib-tools-2.80.0-hde27a5a_6.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/glog-0.7.0-hed5481d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-h59595ed_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmpy2-2.1.5-py310hc3586ac_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gnutls-3.7.9-hb077bed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/graphite2-1.3.13-h59595ed_1003.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gst-plugins-base-1.24.3-h9ad1361_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gstreamer-1.24.3-haf2f30d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/harfbuzz-8.4.0-h3d44ed6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/hdf5-1.14.3-nompi_h4f84152_101.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/icu-73.2-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/imath-3.1.11-hfc55251_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.4-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/jxrlib-1.1-hd590300_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.2-h659d440_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lame-3.100-h166bdaf_1003.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lcms2-2.16-hb7c19ff_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.40-h55db66e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h27087fc_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libabseil-20240116.2-cxx17_h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libaec-1.1.3-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-15.0.2-hefa796f_6_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-acero-15.0.2-hbabe93e_6_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-dataset-15.0.2-hbabe93e_6_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-flight-15.0.2-hc4f8a93_6_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-flight-sql-15.0.2-he4f5ca8_6_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-gandiva-15.0.2-hc1954e9_6_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-substrait-15.0.2-he4f5ca8_6_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-0.22.5-h661eb56_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-devel-0.22.5-h661eb56_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libass-0.17.1-h8fe9dca_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-1_h86c2bf4_netlib.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libboost-1.84.0-h8013b2b_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlicommon-1.1.0-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlidec-1.1.0-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlienc-1.1.0-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcap-2.69-h0f662aa_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-5_h92ddd45_netlib.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libclang-cpp15-15.0.7-default_h127d8a8_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libclang13-18.1.5-default_h5d6823c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcrc32c-1.1.2-h9c3ff4c_0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcublas-*********-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcublas-dev-*********-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcufft-*********-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcufft-dev-*********-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcufile-********-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcufile-dev-********-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcups-2.3.3-h4637d8d_4.conda
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcurand-10.3.0.86-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcurand-dev-10.3.0.86-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcurl-8.7.1-hca28451_0.conda
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcusolver-*********-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcusolver-dev-*********-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcusparse-*********-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcusparse-dev-*********-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.20-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdrm-2.4.120-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20191231-he28a2e2_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libev-4.33-hd590300_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libevent-2.1.12-hf998b51_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.6.2-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.2-h7f98852_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libflac-1.4.3-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-13.2.0-h77fa898_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcrypt-1.10.3-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-0.22.5-h59595ed_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-devel-0.22.5-h59595ed_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-ng-13.2.0-h69a702a_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-13.2.0-hca663fb_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.80.0-hf2295e7_6.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglu-9.0.0-hac7e632_1003.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-2.23.0-h9be4e54_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-storage-2.23.0-hc7a4891_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgpg-error-1.49-h4f305b6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgrpc-1.62.2-h15f2491_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libhwloc-2.10.0-default_h2fb2949_1000.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.17-hd590300_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libidn2-2.3.7-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.0.0-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-5_h92ddd45_netlib.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libllvm15-15.0.7-hb3ce162_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libllvm16-16.0.6-hb3ce162_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libllvm18-18.1.5-hb77312f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnghttp2-1.58.0-h47da74e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnl-3.9.0-hd590300_0.conda
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libnpp-*********-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libnpp-dev-*********-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hd590300_0.conda
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libnvjpeg-*********-0.tar.bz2
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libnvjpeg-dev-*********-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libogg-1.3.4-h7f98852_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-2024.0.0-h2da1b83_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-auto-batch-plugin-2024.0.0-hb045406_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-auto-plugin-2024.0.0-hb045406_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-hetero-plugin-2024.0.0-h5c03a75_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-cpu-plugin-2024.0.0-h2da1b83_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-gpu-plugin-2024.0.0-h2da1b83_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-ir-frontend-2024.0.0-h5c03a75_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-onnx-frontend-2024.0.0-h07e8aee_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-paddle-frontend-2024.0.0-h07e8aee_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-pytorch-frontend-2024.0.0-he02047a_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-tensorflow-frontend-2024.0.0-h39126c6_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-tensorflow-lite-frontend-2024.0.0-he02047a_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopus-1.3.1-h7f98852_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libparquet-15.0.2-hacf5a1f_6_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpciaccess-0.18-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.43-h2797004_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpq-16.2-h33b98f1_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libprotobuf-4.25.3-h08a7969_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libraw-0.21.1-h2a13503_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libre2-11-2023.09.01-h5a48ba9_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsndfile-1.2.2-hc60ed4a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.45.3-h2797004_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libssh2-1.11.0-h0841786_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-13.2.0-hc0a3c3a_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsystemd0-255-h3516f8a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libtasn1-4.19.0-h166bdaf_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libthrift-0.19.0-hb90f79a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.6.0-h1dd3fc0_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libunistring-0.9.10-h7f98852_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libutf8proc-2.8.0-h166bdaf_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libva-2.21.0-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libvorbis-1.3.7-h9c3ff4c_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libvpx-1.14.0-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.4.0-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.15-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcrypt-4.4.36-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxkbcommon-1.7.0-h662e7e4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.12.6-h232c23b_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.2.13-hd590300_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/llvm-openmp-15.0.7-h0cdce71_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lz4-c-1.9.4-hcb278e6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/markupsafe-2.1.5-py310h2372a71_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/metis-5.1.0-h59595ed_1007.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mkl-2023.0.0-h84fe81f_26648.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpc-1.3.1-hfe3b2da_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpfr-4.2.1-h9458935_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpg123-1.32.6-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mpmath-1.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mysql-common-8.3.0-hf1915f5_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mysql-libs-8.3.0-hca2cd23_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.4.20240210-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/nettle-3.9.1-h7ab15ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/networkx-3.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/nsight-compute-2022.3.0.22-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/nspr-4.35-h27087fc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/nss-3.98-h1d7d5a4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-1.26.4-py310hb13e2d6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ocl-icd-2.3.2-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openexr-3.2.2-haf962dd_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openh264-2.4.1-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openjpeg-2.5.2-h488ebb8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.3.0-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/orc-2.0.0-h17fec99_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/p11-kit-0.24.1-hc5aa10d_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.43-hcad00b1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pillow-10.3.0-py310hf73ecf8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pip-24.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pixman-0.43.2-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-h36c2ea0_1001.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pugixml-1.14-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pulseaudio-client-17.0-hb77b528_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyarrow-15.0.2-py310hd207890_6_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha2e5f31_6.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.10.14-hd12c33a_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python_abi-3.10-4_cp310.conda
      - conda: https://conda.anaconda.org/pytorch/linux-64/pytorch-2.2.2-py3.10_cuda11.8_cudnn8.7.0_0.tar.bz2
      - conda: https://conda.anaconda.org/pytorch/linux-64/pytorch-cuda-11.8-h7e8668a_5.tar.bz2
      - conda: https://conda.anaconda.org/pytorch/noarch/pytorch-mutex-1.0-cuda.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyyaml-6.0.1-py310h2372a71_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/qt-main-5.15.8-hc9dc06e_21.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/rdma-core-51.0-hd3aeb46_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/re2-2023.09.01-h7f4b329_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8228510_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.31.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/s2n-1.4.12-h06160fa_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-69.5.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/snappy-1.2.0-hdb0a2a9_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/suitesparse-5.10.1-h5a4f163_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/svt-av1-2.0.0-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sympy-1.12-pypyh9d50eac_103.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tbb-2021.12.0-h00ab1b0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_h4845f30_101.conda
      - conda: https://conda.anaconda.org/pytorch/linux-64/torchtriton-2.2.0-py310.tar.bz2
      - conda: https://conda.anaconda.org/pytorch/linux-64/torchvision-0.17.2-py310_cu118.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.11.0-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2024a-h0c530f3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ucx-1.15.0-ha691c75_8.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/vlfeat-0.9.21-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wheel-0.43.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/x264-1!164.3095-h166bdaf_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/x265-3.5-h924138e_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-0.4.0-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-image-0.4.0-h8ee46fc_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-keysyms-0.4.0-h8ee46fc_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-renderutil-0.3.9-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-wm-0.4.1-h8ee46fc_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xkeyboard-config-2.41-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-fixesproto-5.0-h7f98852_1002.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-kbproto-1.0.7-h7f98852_1002.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libice-1.1.1-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libsm-1.2.4-h7391055_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.9-h8ee46fc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.11-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.3-h7f98852_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxext-1.3.4-h0b41bf4_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxfixes-5.0.3-h7f98852_1004.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrender-0.9.11-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-renderproto-0.11.1-h7f98852_1002.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-xextproto-7.3.0-h0b41bf4_1003.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-xf86vidmodeproto-2.3.1-h7f98852_1002.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-xproto-7.0.31-h7f98852_1007.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xz-5.2.6-h166bdaf_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/yaml-0.2.5-h7f98852_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zlib-1.2.13-hd590300_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.6-ha6fb4c9_0.conda
      - pypi: https://files.pythonhosted.org/packages/a2/ad/e0d3c824784ff121c03cc031f944bc7e139a8f1870ffd2845cc2dd76f6c4/absl_py-2.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6a/00/b08f23b7d7e1e14ce01419a467b583edbb93c6cdb8654e54a9cc579cd61f/addict-2.4.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/14/fd/2f20c40b45e4fb4324834aea24bd4afdf1143390242c0b33774da0e2e34f/anyio-4.3.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/3b/00/2344469e2084fb287c2e0b57b72910309874c3245463acd6cf5e3db69324/appdirs-1.4.4-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a4/6a/e8a041599e78b6b3752da48000b14c8d1e8a04ded09c88c714ba047f34f5/argon2_cffi-23.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/ec/f7/378254e6dd7ae6f31fe40c8649eea7d4832a42243acaf0f1fff9083b2bed/argon2_cffi_bindings-21.2.0-cp36-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/f8/ed/e97229a566617f2ae958a6b13e7cc0f585470eac730a73e9e82c32a3cdd2/arrow-1.3.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/45/86/4736ac618d82a20d87d2f92ae19441ebc7ac9e7a581d7e58bbe79233b24a/asttokens-2.4.1-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/fa/9f/3c3503693386c4b0f245eaf5ca6198e3b28879ca0a40bde6b0e319793453/async_lru-2.0.4-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e0/44/827b2a91a5816512fcaf3cc4ebc465ccd5d598c45cefa6703fcf4a79018f/attrs-23.2.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/50/c3/81e9efb59751b7a151c213f7976ce3bfac9a7786949947afbd60eee279df/av-12.0.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/27/45/377f7e32a5c93d94cd56542349b34efab5ca3f9e2fd5a68c5e93169aa32d/Babel-2.15.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/b1/fe/e8c672695b37eecc5cbf43e1d0638d88d66ba3a44c4d321c796f4e59167f/beautifulsoup4-4.12.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/99/37/e8730c3587a65eb5645d4aba2d27aae48e8003614d6aaf15dda67f702f1f/bidict-0.23.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/ea/63/da7237f805089ecc28a3f36bca6a21c31fcbc2eb380f3b8f1be3312abd14/bleach-6.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/bb/2a/10164ed1f31196a2f7f3799368a821765c62851ead0e630ab52b8e14b4d0/blinker-1.8.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/fb/2b/a64c2d25a37aeb921fddb929111413049fc5f8b9a4c1aefaffaafe768d54/cachetools-5.3.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c9/7c/43d81bdd5a915923c3bad5bb4bff401ea00ccc8e28433fb6083d2e3bf58e/cffi-1.16.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/38/6f/f5fbc992a329ee4e0f288c1fe0e2ad9485ed064cac731ed2fe47dcc38cbf/chardet-5.2.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/00/2e/d53fa4befbf2cfa713304affc7ca780ce4fc1fd8710527771b58311a3229/click-8.1.7-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d1/d6/3965ed04c63042e047cb6a3e6ed1a63a35087b6a609aa3a15ed8ac56c221/colorama-0.4.6-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f3/18/3e867ab37a24fdf073c1617b9c7830e06ec270b1ea4694a624038fc40a03/colorlog-6.8.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/67/38/34ca2481c857c704ce2c44a78df31eed98b05558c87be097a1eb430ec332/comet_ml-3.41.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e6/75/49e5bfe642f71f272236b5b2d2691cf915a7283cc0ceda56357b61daa538/comm-0.2.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/31/28/d28211d29bcc3620b1fece85a65ce5bb22f18670a03cd28ea4b75ede270c/configargparse-1.7.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d3/bb/d10e531b297dd1d46f6b1fd11d018247af9f2d460037554bb7bb9011c6ac/configobj-5.0.8-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/67/0f/6e5b4879594cd1cbb6a2754d9230937be444f404cf07c360c07a10b36aac/contourpy-1.2.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/7b/4e/fa4896744259ee8602464ed2c7330b736cc4dd3fd92f63cd56828bf36707/cryptography-42.0.7-cp39-abi3-manylinux_2_28_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/e7/05/c19819d5e3d95294a6f5947fb9b9629efb316b96de511b418c53d245aae6/cycler-0.12.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/63/22/bc266b167111e70a2da940e78b78d22fea5b1ee32b512ed0789bd6cc2a9f/dash-2.17.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/00/9e/a29f726e84e531a36d56cff187e61d8c96d2cc253c5bcef9a7695acb7e6a/dash_core_components-2.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/75/65/1b16b853844ef59b2742a7de74a598f376ac0ab581f0dcc34db294e5c90e/dash_html_components-2.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/da/ce/43f77dc8e7bbad02a9f88d07bf794eaf68359df756a28bb9f2f78e255bb1/dash_table-5.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7a/27/78d5cf9c7aba43f8341e78273ab776913d2d33beb581ec39b65e56a0db77/debugpy-1.8.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/d5/50/83c593b07763e1161326b3b8c6686f0f4b0f24d5526546bee538c89837d6/decorator-5.1.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/07/6c/aa3f2f849e01cb6a001cd8554a88d4c77c5c1a31c95bdf1cf9301e6d9ef4/defusedxml-0.7.1-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e5/b6/1ed2eb03989ae574584664985367ba70cd9cf8b32ee8cad0e8aaeac819f3/descartes-1.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c9/7a/cef76fd8438a42f96db64ddaa85280485a9c395e7df3db8158cfec1eee34/dill-0.3.8-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f5/e8/f6bd1eee09314e7e6dee49cbe2c5e22314ccdb38db16c9fc72d2fa80d054/docker_pycreds-0.4.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d5/7c/e9fcff7623954d86bdc17782036cbf715ecab1bec4847c008557affe1ca8/docstring_parser-0.16-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/62/45/c663da77e3799057e561432b74967f3348cc985a2bdab433a7b01106611c/dulwich-0.22.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/90/c7/f4abf09c6109c975e462c72ea4f9e2d72d78e20325dafb151c8e9789a31d/embreex-2.17.7.post4-cp310-cp310-manylinux_2_28_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/91/9a/d882fd7562208456236fb2e62b762bf16fbc9ecde842bb871f676ca0f7e1/everett-3.1.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/01/90/79fe92dd413a9cab314ef5c591b5aa9b9ba787ae4cadab75055b0ae00b33/exceptiongroup-1.2.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/80/03/6ea8b1b2a5ab40a7a60dc464d3daa7aa546e0a74d74a9f8ff551ea7905db/executing-2.0.1-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/9c/b9/79691036d4a8f9857e74d1728b23f34f583b81350a27492edda58d5604e1/fastjsonschema-2.19.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/1b/1b/84c63f592ecdfbb3d77d22a8d93c9b92791e4fa35677ad71a7d6449100f8/fire-0.6.0.tar.gz
      - pypi: https://files.pythonhosted.org/packages/61/80/ffe1da13ad9300f87c93af113edd0638c75138c42a0994becfacac078c06/flask-3.0.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/67/09/e09ee013d9d6f2f006147e5fc2b4d807eb2931f4f890c2d4f711e10391d7/fonttools-4.51.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/d5/40/80f36394f336a2f577d3acd1e3edad35fa7d9542afdca0635b38910f38cd/fpsample-0.3.3-cp310-cp310-manylinux_2_28_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/cf/58/8acf1b3e91c58313ce5cb67df61001fc9dcd21be4fadb76c1a2d540e09ed/fqdn-1.5.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/cb/56/f4845ed78723a4eb8eb22bcfcb46e1157a462c78c0a5ed318c68c98f9a79/gdown-5.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/fd/5b/8f0c4a5bb9fd491c277c21eff7ccae71b47d43c4446c9d0c6cff2fe8c2c4/gitdb-4.0.11-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e9/bd/cc3a402a6439c15c3d4294333e13042b915bbeab54edc457c723931fed3f/GitPython-3.1.43-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/47/82/5f51b0ac0e670aa6551f351c6c8a479149a36c413dd76db4b98d26dddbea/grpcio-1.63.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/23/69/2e0eff91272e7b03edac615e00d63fba3b20e831452cbf63bd77cbab9c7a/gsplat-1.4.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/95/04/ff642e65ad6b90db43e668d70ffb6736436c7ce41fcc549f4e9472234127/h11-0.14.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/94/00/94bf8573e7487b7c37f2b613fc381880d48ec2311f2e859b8a5817deb4df/h5py-3.11.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/78/d4/e5d7e4f2174f8a4d63c8897d79eb8fe2503f7ecc03282fee1fa2719c2704/httpcore-1.0.5-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/41/7b/ddacf6dcebb42466abd03f368782142baa82e08fc0c1f8eaa05b4bae87d5/httpx-0.27.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a3/b6/39c7dad203d9984225f47e0aa39ac3ba3a47c77a02d0ef2a7be691855a06/imageio-2.34.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/2d/0a/679461c511447ffaf176567d5c496d1de27cbe34a87df6677d7171b2fbd4/importlib_metadata-7.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/53/9d/40d5207db523363d9b5698f33778c18b0d591e3fdb6e0116b894b2a2491c/ipykernel-6.29.4-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/71/1b/c7bbd3e03ee6f3580a8afbdf8d6fd38279da03bd5c4bc431907ea3246f9a/ipython-8.24.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/70/1a/7edeedb1c089d63ccd8bd5c0612334774e90cf9337de9fe6c82d90081791/ipywidgets-8.1.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7b/55/e5326141505c5d5e34c5e0935d2908a74e4561eca44108fbfb9c13d2911a/isoduration-20.11.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/04/96/92447566d16df59b2a776c0fb82dbc4d9e07cd95062562af01e408583fc4/itsdangerous-2.2.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e9/6e/ce242e8f39919e1af817af194189c5ed2cd2a02dae242563932750787e6b/jaxtyping-0.3.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/20/9f/bc63f0f0737ad7a60800bfd472a4836661adae21f9c2535f3957b1e54ceb/jedi-0.19.1-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/91/29/df4b9b42f2be0b623cbd5e2140cafcaa2bef0759a00b7b70104dcfe2fb51/joblib-1.4.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/8a/3c/4f8791ee53ab9eeb0b022205aa79387119a74cc9429582ce04098e6fc540/json5-0.9.25-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/12/f6/0232cc0c617e195f06f810534d00b74d2f348fe71b2118009ad8ad31f878/jsonpointer-2.4-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c8/2f/324fab4be6fe37fb7b521546e8a557e6cf08c1c1b3d0b4839a00f589d9ef/jsonschema-4.22.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/ee/07/44bd408781594c4d0a027666ef27fab1e441b109dc3b76b4f836f8fd04fe/jsonschema_specifications-2023.12.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/83/df/0f5dd132200728a86190397e1ea87cd76244e42d39ec5e88efd25b2abd7e/jupyter-1.0.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/75/6d/d7b55b9c1ac802ab066b3e5015e90faab1fffbbd67a2af498ffc6cc81c97/jupyter_client-8.6.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/ca/77/71d78d58f15c22db16328a476426f7ac4a60d3a5a7ba3b9627ee2f7903d4/jupyter_console-6.6.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c9/fb/108ecd1fe961941959ad0ee4e12ee7b8b1477247f30b1fdfd83ceaf017f0/jupyter_core-5.7.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a5/94/059180ea70a9a326e1815176b2370da56376da347a796f8c4f0b830208ef/jupyter_events-0.10.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/07/e0/7bd7cff65594fd9936e2f9385701e44574fc7d721331ff676ce440b14100/jupyter_lsp-2.2.5-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/95/85/483b8e09a897d1bc2194646d30d4ce6ae166106e91ecbd11d6b6d9ccfc36/jupyter_server-2.13.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/07/2d/2b32cdbe8d2a602f697a649798554e4f072115438e92249624e532e8aca6/jupyter_server_terminals-0.5.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/72/c3/532326adbb2b76f709e3e582aeefd0a85bd7454599ff450d90dd9540f5ed/jupyterlab-4.2.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/b1/dd/ead9d8ea85bf202d90cc513b533f9c363121c7792674f78e0d8a854b63b4/jupyterlab_pygments-0.3.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/2f/b9/ed4ecad7cf1863a64920dc4c19b0376628b5d6bd28d2ec1e00cbac4ba2fb/jupyterlab_server-2.27.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/24/da/db1cb0387a7e4086780aff137987ee924e953d7f91b2a870f994b9b1eeb8/jupyterlab_widgets-3.0.10-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6f/40/4ab1fdb57fced80ce5903f04ae1aed7c1d5939dda4fd0c0aa526c12fe28a/kiwisolver-1.4.5-cp310-cp310-manylinux_2_12_x86_64.manylinux2010_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/83/60/d497a310bde3f01cb805196ac61b7ad6dc5dcf8dce66634dc34364b20b4f/lazy_loader-0.4-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/5e/9e/e7768a8e363fc6f0c978bb7a0aa7641f10d80be60000e788ef2f01d34a7c/lightning_utilities-0.11.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/61/8a/a0a71720da1c61fd6a55a1962ec1280cebc4552c319efe744c8aa99d28c5/lxml-5.2.1-cp310-cp310-manylinux_2_28_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/a9/6d/8b08b54435ee0b2b1d0d3b9a5e212cc42f4dce08cd6e2441b3b9d216471a/mapbox_earcut-1.0.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/fc/b3/0c0c994fe49cd661084f8d5dc06562af53818cc0abefaca35bdc894577c3/Markdown-3.6-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/42/d7/1ec15b46af6af88f19b8e5ffea08fa375d433c998b8a7639e76935c14f1f/markdown_it_py-3.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d6/07/061f97211f942101070a46fecd813a6b1bd83590ed7b07c473cabd707fe7/matplotlib-3.8.4-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/8f/8e/9ad090d3553c280a8060fbf6e24dc1c0c29704ee7d1c372f0c174aa59285/matplotlib_inline-0.1.7-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/b3/38/89ba8ad64ae25be8de66a6d463314cf1eb366222074cfda9ee839c56a4b4/mdurl-0.1.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/1a/26/583ff25923efd88c90733a0fad51890c04e3367663b55548d0c9ed0a658c/mediapy-1.2.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f0/74/c95adcdf032956d9ef6c89a9b8a5152bf73915f8c633f3e3d88d06bd699c/mistune-3.0.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d9/96/a1868dd8997d65732476dfc70fef44d046c1b4dbe36ec1481ab744d87775/msgpack-1.0.8-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/9b/5d/f25ac7d4fb77cbd53ddc6d05d833c6bf52b12770a44fa9a447eed470ca9a/msgpack_numpy-0.4.8-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c6/65/080509c5774a1592b2779d902a70b5fe008532759927e011f068145a16cb/msgspec-0.19.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/bc/f7/7ec7fddc92e50714ea3745631f79bd9c96424cb2702632521028e57d3a36/multiprocess-0.70.16-py310-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/66/e8/00517a23d3eeaed0513e718fbc94aab26eaa1758f5690fc8578839791c79/nbclient-0.10.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/b8/bb/bb5b6a515d1584aa2fd89965b11db6632e4bdc69495a52374bcc36e56cfa/nbconvert-7.16.4-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a9/82/0340caa499416c78e5d8f5f05947ae4bc3cba53c9f038ab6e9ed964e22f1/nbformat-5.10.4-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f4/16/e93ebb619a3edd8851df21425fe20b7491bd1d819fc19beb9bfc1bf2556e/nerfacc-0.5.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a0/c4/c2971a3ba4c6103a3d10c4b0f24f461ddc027f0f09763220cf35ca1401b3/nest_asyncio-1.6.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6d/92/8d7aebd4430ab5ff65df2bfee6d5745f95c004284db2d8ca76dcbfd9de47/ninja-********-py2.py3-none-manylinux1_x86_64.manylinux_2_5_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/1a/e6/6d2ead760a9ddb35e65740fd5a57e46aadd7b0c49861ab24f94812797a1c/nodeenv-1.8.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f2/57/2f8d59ddc7f2d0d8ac4f80f869545bc44646fc78c1c083b3655c58e3edfb/notebook-7.0.7-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f9/33/bd5b9137445ea4b680023eb0469b2bb969d61303dedb2aac6560ff3d14a1/notebook_shim-0.2.4-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c6/53/460bf754677b3b247fb99a447e3575490dbc5f42ec94d528bc0137176f6a/nuscenes_devkit-1.1.9-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/3b/e1/fc609763d982c6c43ee9503279fa6dc42d048b6224a9dc0a0ce8ac19308a/open3d-0.18.0-cp310-cp310-manylinux_2_27_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/f5/d0/2e455d894ec0d6527e662ad55e70c04f421ad83a6fd0a54c3dd73c411282/opencv_python-********-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/d1/09/248f86a404567303cdf120e4a301f389b68e3b18e5c0cc428de327da609c/opencv_python_headless-*********-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/2c/ab/fc8290c6a4c722e5514d80f62b2dc4c4df1a68a41d1364e625c35990fcf3/overrides-7.7.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/49/df/1fceb2f8900f8639e278b056416d49134fb8d84c5942ffaa01ad34782422/packaging-24.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/89/1b/12521efcbc6058e2673583bb096c2b5046a9df39bd73eca392c1efed24e5/pandas-2.2.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/ef/af/4fbc8cab944db5d21b7e2a5b8e9211a03a79852b1157e2c102fcc61ac440/pandocfilters-1.5.1-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c6/ac/dac4a63f978e4dcb3c6d3a78c4d8e0192a113d288502a1216950c41b1027/parso-0.8.4-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f4/7f/cea34872c000d17972dad998575d14656d7c6bcf1a08a8d66d73c1ef2cca/pathos-0.3.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/9e/c3/059298687310d527a58bb01f3b1965787ee3b40dce76752eda8b44e9a2c5/pexpect-4.9.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/b0/15/1691fa5aaddc0c4ea4901c26f6137c29d5f6673596fe960a0340e8c308e1/platformdirs-4.2.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/0b/f8/b65cdd2be32e442c4efe7b672f73c90b05eab5a7f3f4115efe181d432c60/plotly-5.22.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/49/ac/3d0ca39b304cbeee43a80ac8ea5ba85576373c00e30fb3e1c5d51bfa7e20/plyfile-1.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e1/d7/9e73c32f73da71e8224b4cb861b5db50ebdebcdff14d3e3fb47a63c578b2/pox-0.3.4-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/ff/fa/5160c7d2fb1d4f2b83cba7a40f0eb4b015b78f6973b7ab6b2e73c233cfdc/ppft-*******-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6d/8e/2df7467a15eae40e26c476683962fdb810cd1b36676603e2f139b4abbeaf/pretty_errors-1.2.25-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c7/98/745b810d822103adca2df8decd4c0bbe839ba7ad3511af3f0d09692fc0f0/prometheus_client-0.20.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/ee/fd/ca7bf3869e7caa7a037e23078539467b433a4e01eebd93f77180ab927766/prompt_toolkit-3.0.43-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/31/be/80a9c6f16dfa4d41be3edbe655349778ae30882407fa8275eb46b4d34854/protobuf-3.20.3-cp310-cp310-manylinux_2_12_x86_64.manylinux2010_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/c5/4f/0e22aaa246f96d6ac87fe5ebb9c5a693fbe8877f537a1022527c47ca43c5/psutil-5.9.8-cp36-abi3-manylinux_2_12_x86_64.manylinux2010_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/22/a6/858897256d0deac81a172289110f31629fc4cee19b6f01283303e18c8db3/ptyprocess-0.7.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/2b/27/77f9d5684e6bce929f5cfe18d6cfbe5133013c06cb2fbf5933670e60761d/pure_eval-0.2.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/ba/64/0451cf41a00fd5ac4501de4ea0e395b7d909e09d665e56890b5d3809ae26/pycocotools-2.0.7-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/dc/f1/5e81108414287278a01f1642271d7885e2aebc2bd10e7cf744d8c4cf0955/pycollada-0.8.tar.gz
      - pypi: https://files.pythonhosted.org/packages/13/a3/a812df4e2dd5696d1f351d58b8fe16a405b234ad2886a0dab9183fb78109/pycparser-2.22-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f7/3f/01c8b82017c199075f8f788d0d906b9ffbbc5a47dc9918a945e13d5a2bda/pygments-2.18.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/2c/ba/a4bc465d36f6aafbff89da1bf67bcc6a97475b1d2300a74a778dcb293cef/pyliblzfse-0.4.1.tar.gz
      - pypi: https://files.pythonhosted.org/packages/44/b2/5e98847b924748ec293bac6a68bb7d203a9ec328dbf7ef9fb886fd0e2c07/pymeshlab-2022.2.post3-cp310-cp310-manylinux1_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/cb/55/68b89d526e8331724665dcded0a32a76d73d6bcac41cc56084fda8e25486/pyngrok-7.1.6-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/9d/ea/6d76df31432a0e6fdf81681a895f009a4bb47b3c39036db3e1b528191d52/pyparsing-3.1.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/49/b3/d8482e8cacc8ea15a356efea13d22ce1c5914a9ee36622ba250523240bf2/pyquaternion-0.9.9-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6f/32/3c865e7d62e481c46abffef3303db0d27bf2ca72e4f497d05d66939bfd4a/python_box-6.1.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/ec/57/56b9bcc3c9c6a792fcbaf139543cee77261f3651ca9da0c93f5c1221264b/python_dateutil-2.9.0.post0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/fe/e5/03fa8e76c718e1dd7fb5b74e9ce816ae0e08734080b1e98dbafbcf2fc8ba/python_engineio-4.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/35/a6/145655273568ee78a581e734cf35beb9e33a370b29c5d3c8fee3744de29f/python_json_logger-2.0.7-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/af/bf/be12875b17709b591d8505811e513a88b31316e4ce0e801da351b4765ea5/python_socketio-5.11.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e2/8c/856047f955acc30179e9255fdc488059ca22f0938519523d53494f7cfee8/pytorch_msssim-1.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/9c/3d/a121f284241f08268b21359bd425f7d4825cffc5ac5cd0e1b3d82ffd2b10/pytz-2024.1-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/40/4f/088d0fe18b188a0754483b7d632a97ef608dce80c2648219d071c9f1715c/pyzmq-26.0.3-cp310-cp310-manylinux_2_28_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/f2/3f/de5e5eb44900c1ed1c1567bc505e3b6e6f4c01cf29e558bf2f8cee29af5b/qtconsole-5.5.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7e/a9/2146d5117ad8a81185331e0809a6b48933c10171f5bac253c6df9fce991c/QtPy-2.4.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/72/2f/80c24a0f24c3a8ae2a9b8cfb579bba0ddcdef9bfd420b98ce69a51b754d2/rawpy-0.21.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/b7/59/2056f61236782a2c86b33906c025d4f4a0b17be0161b63b70fd9e8775d36/referencing-0.35.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/3f/51/d4db610ef29373b879047326cbf6fa98b6c1969d6f6dc423279de2b1be2c/requests_toolbelt-1.0.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/8f/04/9e36f28be4c0532c0e9207ff9dc01fb13a2b0eb036476a213b0000837d0e/retrying-1.3.4-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7b/44/4e421b96b67b2daff264473f7465db72fbdf36a07e05494f50300cc7b0c6/rfc3339_validator-0.1.4-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/9e/51/17023c0f8f1869d8806b979a2bffa3f861f26a3f1a66b094288323fba52f/rfc3986_validator-0.1.1-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/87/67/a37f6214d0e9fe57f6ae54b2956d550ca8365857f42a1ce0392bb21d9410/rich-13.7.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e5/20/10c12b1acb102c4981a7e1dc86b60e36c1d5c940a7bda48643542f80dbff/rpds_py-0.18.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/59/a5/176d27468a1b0bcd7fa9c011cadacfa364e9bca8fa649baab7fb3f15af70/Rtree-1.2.0-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/ea/82/264b5d0f2f4c4ec073e59dd1ac1ed5fa85f54bec2dd4cc231fcdfd12ba42/scikit_image-0.23.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/8f/38/420ee614359d8f453ffe2bb5c2e963bf50459d9bbd3f5a92aa9059658955/scikit_learn-1.4.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/b9/9d/39dbcf49a793157f9d4f5b8961855677eb4dbb4b82700dcee7042ad2310c/scipy-1.13.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/6a/23/8146aad7d88f4fcb3a6218f41a60f6c2d4e3a72de72da1825dc7c8f7877c/semantic_version-2.10.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/40/b0/4562db6223154aa4e22f939003cb92514c79f3d4dccca3444253fd17f902/Send2Trash-1.8.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/cf/58/cfbed3fdc41891e9d2b45a5dbe706dcaf35429eb1446e30a504bb2ea9ea2/sentry_sdk-2.1.1-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/79/e7/54b36be02aee8ad573be68f6f46fd62838735c2f007b22df50eb5e13a20d/setproctitle-1.3.3-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/81/77/e1475695606a8305c9ad5f5132d911abe8ed1655a6f5c817a69bdd2b5324/shapely-2.0.4-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/e2/d1/a1d3189e7873408b9dc396aef0d7926c198b0df2aa3ddb5b539d3e89a70f/shtab-1.7.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6d/ea/288a8ac1d9551354488ff60c0ac6a76acc3b6b60f0460ac1944c75e240da/simple_websocket-1.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/cb/b6/ed513a0adc3e2c9654864ffb68266dcab5720d5653428d690e7e4fb32a6c/simplejson-3.19.2-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/d9/5a/e7c31adbe875f2abbb91bd84cf2dc52d792b5a01506781dbcf25c91daf11/six-1.16.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a7/a5/10f97f73544edcdef54409f1d839f6049a0d79df68adbc1ceb24d1aaca42/smmap-5.0.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e9/44/75a9c9421471a6c4805dbf2356f7c181a29c1879239abab1ea2cc8f38b40/sniffio-1.3.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/4c/f3/038b302fdfbe3be7da016777069f26ceefe11a681055ea1f7817546508e3/soupsieve-2.5-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/cd/a4/518bee82ace2681327e471f8f59e9958760a564775a8f650489da61205d3/splines-0.3.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f1/7b/ce1eafaf1a76852e2ec9b22edecf1daa58175c090266e9f6c64afcd81d91/stack_data-0.6.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d6/ea/ec6101e1710ac74e88b10312e9b59734885155e47d7dbb1171e4d347a364/svg.path-6.3-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/61/a1/6bb0cbebefb23641f068bb58a2bc56da9beb2b1c550242e3c540b37698f3/tenacity-8.3.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/3a/d0/b97889ffa769e2d1fdebb632084d5e8b53fc299d43a537acee7ec0c021a3/tensorboard-2.16.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7a/13/e503968fefabd4c6b2650af21e110aa8466fe21432cd7c43a84577a89438/tensorboard_data_server-0.7.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c3/ad/83380fb3c85ddf68c509059cf328374c1878f87382070475abc72867c7ba/tensorly-0.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d9/5f/8c716e47b3a50cbd7c146f45881e11d9414def768b7cd9c5e6650ec2a80a/termcolor-2.4.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6a/9e/2064975477fdc887e47ad42157e214526dcad8f317a948dee17e1659a62f/terminado-0.18.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/4b/2c/ffbf7a134b9ab11a67b0cf0726453cedd9c5043a4fe7a35d1cefa9a1bcfb/threadpoolctl-3.5.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c1/cf/dd1cdf85db58c811816377afd6ba8a240f4611e16f4085201598fb2d5578/tifffile-2024.5.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/72/ed/358a8bc5685c31c0fe7765351b202cf6a8c087893b5d2d64f63c950f8beb/timm-0.6.7-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/2c/4d/0db5b8a613d2a59bbc29bc5bb44a2f8070eb9ceab11c50d477502a8a0092/tinycss2-1.3.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/97/75/10a9ebee3fd790d20926a90a2547f0bf78f371b2f13aa822c759680ca7b9/tomli-2.0.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/9f/2c/e24c7e261eaa00fc911c39a5e30f77efbace480aae2548db9ceaef410945/torch_fidelity-0.3.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/aa/d9/b235d32de0f496492b108db7aea0cda42c58b713e2547a149385f1dfc8e3/torchmetrics-1.4.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/9f/12/11d0a757bb67278d3380d41955ae98527d5ad18330b2edbdc8de222b569b/tornado-6.4-cp38-abi3-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/18/eb/fdb7eb9e48b7b02554e1664afd3bd3f117f6b6d6c5881438a0b055554f9b/tqdm-4.66.4-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/00/c0/8f5d070730d7836adc9c9b6408dec68c6ced86b304a9b26a14df072a6e8c/traitlets-5.14.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/57/7b/557a411622ff3e144d52bebbf8ad8a28bb18ee55ff68e1eb5ebfce755975/trimesh-4.3.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/cf/4b/9a77dc721aa0b7f74440a42e4ef6f9a4fae7324e17f64f88b96f4c25cc05/typeguard-4.4.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c7/1b/af4f4c4f3f7339a4b7eb3c0ab13416db98f8ac09de3399129ee5fdfa282b/types_python_dateutil-2.9.0.20240316-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/4d/b1/c0c62adcd5e64244af876a3173f194d02dc1fe830cae28370b6d9e315971/tyro-0.9.17-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/0f/dd/84f10e23edd882c6f968c21c2434fe67bd4a528967067515feca9e611e5e/tzdata-2025.1-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e7/00/3fca040d7cf8a32776d3d81a00c8ee7457e00f80c649f1e4a863c8321ae9/uri_template-1.3.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7e/af/d3af73e45f8f2d1f3134df23f3832f2b248efadbc4a75d2de2fced454a20/vhacdx-0.0.6-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/1b/fe/04034c0fb25f7b823b331c20da35f6f688cadbc57c00f1e0591d88ba6caf/viser-0.2.7-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7b/69/cfb1af44622044d4db0cad65721d283a921a4795f0ad121616b9eaa6ccd7/wadler_lindig-0.1.4-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/b6/49/fd266df341165c7ed94f0e9875bb20cc75a59b1dd9b6f6a51a31db5f8527/wandb-0.17.0-py3-none-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/fd/84/fd2ba7aafacbad3c4201d395674fc6348826569da3c0937e75505ead3528/wcwidth-0.2.13-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d5/e1/3e9013159b4cbb71df9bd7611cbf90dc2c621c8aeeb677fc41dad72f2261/webcolors-1.13-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f4/24/2a3e3df732393fed8b3ebf2ec078f05546de641fe1b667ee316ec1dcf3b7/webencodings-0.5.1-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/67/b4/91683d7d5f66393e8877492fe4763304f82dbe308658a8db98f7a9e20baf/websocket_client-1.3.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/9a/12/c7a7504f5bf74d6ee0533f6fc7d30d8f4b79420ab179d1df2484b07602eb/websockets-12.0-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/9d/6e/e792999e816d19d7fcbfa94c730936750036d65656a76a5a688b57a656c4/werkzeug-3.0.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/99/bc/82a8c3985209ca7c0a61b383c80e015fd92e74f8ba0ec1af98f9d6ca8dce/widgetsnbextension-4.0.10-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/49/83/b40bc1ad04a868b5b5bcec86349f06c1ee1ea7afe51dc3e46131e4f39308/wrapt-1.16.0-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/78/58/e860788190eba3bcce367f74d29c4675466ce8dddfba85f7827588416f01/wsproto-1.2.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/51/23/33f2ed4247e30770d964e3952bf4cbe13d22846eca940557447e0a4a2125/wurlitzer-3.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/29/ee/086c7cb4f067238e55fd645188232d67afe1ac6adacbea7a8cf2c6346cd8/xatlas-0.0.9-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/80/8a/1dd41557883b6196f8f092011a5c1f72d4d44cf36d7b67d4a5efe3127949/xxhash-3.4.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/a5/a3/b182c56518f208e3b10a979a24dbe7348fc39edf19e6271b6cf5f8988c96/yourdfpy-0.0.56-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c2/0a/ba9d0ee9536d3ef73a3448e931776e658b36f128d344e175bc32b092a8bf/zipp-3.18.1-py3-none-any.whl
      - pypi: .
packages:
- kind: conda
  name: _libgcc_mutex
  version: '0.1'
  build: conda_forge
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
  sha256: fe51de6107f9edc7aa4f786a70f4a883943bc9d39b3bb7307c04c41410990726
  md5: d7c89558ba9fa0495403155b64376d81
  license: None
  size: 2562
  timestamp: 1578324546067
- kind: conda
  name: _openmp_mutex
  version: '4.5'
  build: 2_kmp_llvm
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_kmp_llvm.tar.bz2
  sha256: 84a66275da3a66e3f3e70e9d8f10496d807d01a9e4ec16cd2274cc5e28c478fc
  md5: 562b26ba2e19059551a811e72ab7f793
  depends:
  - _libgcc_mutex 0.1 conda_forge
  - llvm-openmp >=9.0.1
  license: BSD-3-Clause
  license_family: BSD
  size: 5744
  timestamp: 1650742457817
- kind: pypi
  name: absl-py
  version: 2.1.0
  url: https://files.pythonhosted.org/packages/a2/ad/e0d3c824784ff121c03cc031f944bc7e139a8f1870ffd2845cc2dd76f6c4/absl_py-2.1.0-py3-none-any.whl
  sha256: 526a04eadab8b4ee719ce68f204172ead1027549089702d99b9059f129ff1308
  requires_python: '>=3.7'
- kind: pypi
  name: addict
  version: 2.4.0
  url: https://files.pythonhosted.org/packages/6a/00/b08f23b7d7e1e14ce01419a467b583edbb93c6cdb8654e54a9cc579cd61f/addict-2.4.0-py3-none-any.whl
  sha256: 249bb56bbfd3cdc2a004ea0ff4c2b6ddc84d53bc2194761636eb314d5cfa5dfc
- kind: conda
  name: alsa-lib
  version: 1.2.11
  build: hd590300_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/alsa-lib-1.2.11-hd590300_1.conda
  sha256: 0e2b75b9834a6e520b13db516f7cf5c9cea8f0bbc9157c978444173dacb98fec
  md5: 0bb492cca54017ea314b809b1ee3a176
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-or-later
  license_family: GPL
  size: 554699
  timestamp: 1709396557528
- kind: pypi
  name: anyio
  version: 4.3.0
  url: https://files.pythonhosted.org/packages/14/fd/2f20c40b45e4fb4324834aea24bd4afdf1143390242c0b33774da0e2e34f/anyio-4.3.0-py3-none-any.whl
  sha256: 048e05d0f6caeed70d731f3db756d35dcc1f35747c8c403364a8332c630441b8
  requires_dist:
  - idna>=2.8
  - sniffio>=1.1
  - exceptiongroup>=1.0.2 ; python_version < '3.11'
  - typing-extensions>=4.1 ; python_version < '3.11'
  - packaging ; extra == 'doc'
  - sphinx>=7 ; extra == 'doc'
  - sphinx-rtd-theme ; extra == 'doc'
  - sphinx-autodoc-typehints>=1.2.0 ; extra == 'doc'
  - anyio[trio] ; extra == 'test'
  - coverage[toml]>=7 ; extra == 'test'
  - exceptiongroup>=1.2.0 ; extra == 'test'
  - hypothesis>=4.0 ; extra == 'test'
  - psutil>=5.9 ; extra == 'test'
  - pytest>=7.0 ; extra == 'test'
  - pytest-mock>=3.6.1 ; extra == 'test'
  - trustme ; extra == 'test'
  - uvloop>=0.17 ; (platform_python_implementation == 'CPython' and platform_system != 'Windows') and extra == 'test'
  - trio>=0.23 ; extra == 'trio'
  requires_python: '>=3.8'
- kind: conda
  name: aom
  version: 3.9.0
  build: hac33072_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aom-3.9.0-hac33072_0.conda
  sha256: eef9b630ec0d3a3835c388b00685002d67d1d44db2af6c734921bdf65035654f
  md5: 93a3bf248e5bc729807db198a9c89f07
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 2701135
  timestamp: 1713925435201
- kind: pypi
  name: appdirs
  version: 1.4.4
  url: https://files.pythonhosted.org/packages/3b/00/2344469e2084fb287c2e0b57b72910309874c3245463acd6cf5e3db69324/appdirs-1.4.4-py2.py3-none-any.whl
  sha256: a841dacd6b99318a741b166adb07e19ee71a274450e68237b4650ca1055ab128
- kind: pypi
  name: argon2-cffi
  version: 23.1.0
  url: https://files.pythonhosted.org/packages/a4/6a/e8a041599e78b6b3752da48000b14c8d1e8a04ded09c88c714ba047f34f5/argon2_cffi-23.1.0-py3-none-any.whl
  sha256: c670642b78ba29641818ab2e68bd4e6a78ba53b7eff7b4c3815ae16abf91c7ea
  requires_dist:
  - argon2-cffi-bindings
  - typing-extensions ; python_version < '3.8'
  - argon2-cffi[tests,typing] ; extra == 'dev'
  - tox>4 ; extra == 'dev'
  - furo ; extra == 'docs'
  - myst-parser ; extra == 'docs'
  - sphinx ; extra == 'docs'
  - sphinx-copybutton ; extra == 'docs'
  - sphinx-notfound-page ; extra == 'docs'
  - hypothesis ; extra == 'tests'
  - pytest ; extra == 'tests'
  - mypy ; extra == 'typing'
  requires_python: '>=3.7'
- kind: pypi
  name: argon2-cffi-bindings
  version: 21.2.0
  url: https://files.pythonhosted.org/packages/ec/f7/378254e6dd7ae6f31fe40c8649eea7d4832a42243acaf0f1fff9083b2bed/argon2_cffi_bindings-21.2.0-cp36-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: b746dba803a79238e925d9046a63aa26bf86ab2a2fe74ce6b009a1c3f5c8f2ae
  requires_dist:
  - cffi>=1.0.1
  - pytest ; extra == 'dev'
  - cogapp ; extra == 'dev'
  - pre-commit ; extra == 'dev'
  - wheel ; extra == 'dev'
  - pytest ; extra == 'tests'
  requires_python: '>=3.6'
- kind: pypi
  name: arrow
  version: 1.3.0
  url: https://files.pythonhosted.org/packages/f8/ed/e97229a566617f2ae958a6b13e7cc0f585470eac730a73e9e82c32a3cdd2/arrow-1.3.0-py3-none-any.whl
  sha256: c728b120ebc00eb84e01882a6f5e7927a53960aa990ce7dd2b10f39005a67f80
  requires_dist:
  - python-dateutil>=2.7.0
  - types-python-dateutil>=2.8.10
  - doc8 ; extra == 'doc'
  - sphinx>=7.0.0 ; extra == 'doc'
  - sphinx-autobuild ; extra == 'doc'
  - sphinx-autodoc-typehints ; extra == 'doc'
  - sphinx-rtd-theme>=1.3.0 ; extra == 'doc'
  - dateparser==1.* ; extra == 'test'
  - pre-commit ; extra == 'test'
  - pytest ; extra == 'test'
  - pytest-cov ; extra == 'test'
  - pytest-mock ; extra == 'test'
  - pytz==2021.1 ; extra == 'test'
  - simplejson==3.* ; extra == 'test'
  requires_python: '>=3.8'
- kind: pypi
  name: asttokens
  version: 2.4.1
  url: https://files.pythonhosted.org/packages/45/86/4736ac618d82a20d87d2f92ae19441ebc7ac9e7a581d7e58bbe79233b24a/asttokens-2.4.1-py2.py3-none-any.whl
  sha256: 051ed49c3dcae8913ea7cd08e46a606dba30b79993209636c4875bc1d637bc24
  requires_dist:
  - six>=1.12.0
  - typing ; python_version < '3.5'
  - astroid<2,>=1 ; python_version < '3' and extra == 'astroid'
  - astroid<4,>=2 ; python_version >= '3' and extra == 'astroid'
  - pytest ; extra == 'test'
  - astroid<2,>=1 ; python_version < '3' and extra == 'test'
  - astroid<4,>=2 ; python_version >= '3' and extra == 'test'
- kind: pypi
  name: async-lru
  version: 2.0.4
  url: https://files.pythonhosted.org/packages/fa/9f/3c3503693386c4b0f245eaf5ca6198e3b28879ca0a40bde6b0e319793453/async_lru-2.0.4-py3-none-any.whl
  sha256: ff02944ce3c288c5be660c42dbcca0742b32c3b279d6dceda655190240b99224
  requires_dist:
  - typing-extensions>=4.0.0 ; python_version < '3.11'
  requires_python: '>=3.8'
- kind: conda
  name: attr
  version: 2.5.1
  build: h166bdaf_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/attr-2.5.1-h166bdaf_1.tar.bz2
  sha256: 82c13b1772c21fc4a17441734de471d3aabf82b61db9b11f4a1bd04a9c4ac324
  md5: d9c69a24ad678ffce24c6543a0176b00
  depends:
  - libgcc-ng >=12
  license: GPL-2.0-or-later
  license_family: GPL
  size: 71042
  timestamp: 1660065501192
- kind: pypi
  name: attrs
  version: 23.2.0
  url: https://files.pythonhosted.org/packages/e0/44/827b2a91a5816512fcaf3cc4ebc465ccd5d598c45cefa6703fcf4a79018f/attrs-23.2.0-py3-none-any.whl
  sha256: 99b87a485a5820b23b879f04c2305b44b951b502fd64be915879d77a7e8fc6f1
  requires_dist:
  - importlib-metadata ; python_version < '3.8'
  - attrs[tests] ; extra == 'cov'
  - coverage[toml]>=5.3 ; extra == 'cov'
  - attrs[tests] ; extra == 'dev'
  - pre-commit ; extra == 'dev'
  - furo ; extra == 'docs'
  - myst-parser ; extra == 'docs'
  - sphinx ; extra == 'docs'
  - sphinx-notfound-page ; extra == 'docs'
  - sphinxcontrib-towncrier ; extra == 'docs'
  - towncrier ; extra == 'docs'
  - zope-interface ; extra == 'docs'
  - attrs[tests-no-zope] ; extra == 'tests'
  - zope-interface ; extra == 'tests'
  - mypy>=1.6 ; (platform_python_implementation == 'CPython' and python_version >= '3.8') and extra == 'tests-mypy'
  - pytest-mypy-plugins ; (platform_python_implementation == 'CPython' and python_version >= '3.8') and extra == 'tests-mypy'
  - attrs[tests-mypy] ; extra == 'tests-no-zope'
  - cloudpickle ; platform_python_implementation == 'CPython' and extra == 'tests-no-zope'
  - hypothesis ; extra == 'tests-no-zope'
  - pympler ; extra == 'tests-no-zope'
  - pytest-xdist[psutil] ; extra == 'tests-no-zope'
  - pytest>=4.3.0 ; extra == 'tests-no-zope'
  requires_python: '>=3.7'
- kind: pypi
  name: av
  version: 12.0.0
  url: https://files.pythonhosted.org/packages/50/c3/81e9efb59751b7a151c213f7976ce3bfac9a7786949947afbd60eee279df/av-12.0.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: 39f0b4cfb89f4f06b339c766f92648e798a96747d4163f2fa78660d1ab1f1b5e
  requires_python: '>=3.8'
- kind: conda
  name: aws-c-auth
  version: 0.7.18
  build: he0b1f16_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-c-auth-0.7.18-he0b1f16_0.conda
  sha256: 03dbdc7a3bb8a92d5404fcfc2ff2a68037860bb8a54dae345384b54f206c638f
  md5: 5f4ec63692861f4a812898a4fbd5cc20
  depends:
  - aws-c-cal >=0.6.11,<0.6.12.0a0
  - aws-c-common >=0.9.15,<0.9.16.0a0
  - aws-c-http >=0.8.1,<0.8.2.0a0
  - aws-c-io >=0.14.7,<0.14.8.0a0
  - aws-c-sdkutils >=0.1.15,<0.1.16.0a0
  - libgcc-ng >=12
  license: Apache-2.0
  license_family: Apache
  size: 104635
  timestamp: 1713478222324
- kind: conda
  name: aws-c-cal
  version: 0.6.11
  build: heb1d5e4_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-c-cal-0.6.11-heb1d5e4_0.conda
  sha256: f1b40106a70cc294aab350daa97c760a9875073f58a5b7a25370c31fed8a2c15
  md5: 98784bb35b316e2ba8698f4a75326e9a
  depends:
  - aws-c-common >=0.9.15,<0.9.16.0a0
  - libgcc-ng >=12
  - openssl >=3.2.1,<4.0a0
  license: Apache-2.0
  license_family: Apache
  size: 46257
  timestamp: 1712494861919
- kind: conda
  name: aws-c-common
  version: 0.9.15
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-c-common-0.9.15-hd590300_0.conda
  sha256: e4251e5fa2656140628f40b74e61cf5048dfd4346f6d81517d346b371113496e
  md5: ad8955a300fd09e97e76c38638ac7157
  depends:
  - libgcc-ng >=12
  license: Apache-2.0
  license_family: Apache
  size: 226559
  timestamp: 1712101677803
- kind: conda
  name: aws-c-compression
  version: 0.2.18
  build: hce8ee76_3
  build_number: 3
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-c-compression-0.2.18-hce8ee76_3.conda
  sha256: ab0617f2d66d5d88fc6c7edb6ecd4589e0a744ccaeff95765371c9cabdb29722
  md5: b19224a5179ecb512c4aac9f8a6d57a7
  depends:
  - aws-c-common >=0.9.15,<0.9.16.0a0
  - libgcc-ng >=12
  license: Apache-2.0
  license_family: Apache
  size: 19134
  timestamp: 1712138634166
- kind: conda
  name: aws-c-event-stream
  version: 0.4.2
  build: h01f5eca_8
  build_number: 8
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-c-event-stream-0.4.2-h01f5eca_8.conda
  sha256: 688b81ed93151868df2717556d3b93dcfaf6bf129a1474f14e0c993095816d3f
  md5: afb85fc0f01032d115c57c961950e7d8
  depends:
  - aws-c-common >=0.9.15,<0.9.16.0a0
  - aws-c-io >=0.14.7,<0.14.8.0a0
  - aws-checksums >=0.1.18,<0.1.19.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: Apache-2.0
  license_family: Apache
  size: 53700
  timestamp: 1712507243610
- kind: conda
  name: aws-c-http
  version: 0.8.1
  build: hdb68c23_10
  build_number: 10
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-c-http-0.8.1-hdb68c23_10.conda
  sha256: a13e77f6b40de79b33711f70b8180943053cc162efdb357bc9cd577f0ac69818
  md5: cb6065938167da2d2f078c2f08473b84
  depends:
  - aws-c-cal >=0.6.11,<0.6.12.0a0
  - aws-c-common >=0.9.15,<0.9.16.0a0
  - aws-c-compression >=0.2.18,<0.2.19.0a0
  - aws-c-io >=0.14.7,<0.14.8.0a0
  - libgcc-ng >=12
  license: Apache-2.0
  license_family: Apache
  size: 195362
  timestamp: 1712654535499
- kind: conda
  name: aws-c-io
  version: 0.14.7
  build: hbfbeace_6
  build_number: 6
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-c-io-0.14.7-hbfbeace_6.conda
  sha256: 10c8df9b71be8aba9b1aad48b123fc81896eb7b73c686042bed4a9e77d92e812
  md5: d6382461de9a91a2665e964f92d8da0a
  depends:
  - aws-c-cal >=0.6.11,<0.6.12.0a0
  - aws-c-common >=0.9.15,<0.9.16.0a0
  - libgcc-ng >=12
  - s2n >=1.4.12,<1.4.13.0a0
  license: Apache-2.0
  license_family: Apache
  size: 158124
  timestamp: 1713346977725
- kind: conda
  name: aws-c-mqtt
  version: 0.10.4
  build: h50844eb_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-c-mqtt-0.10.4-h50844eb_0.conda
  sha256: a6588943583636337ab4fb6233df7b8fc5e42199dafc415d5b8a968a7ff11a8f
  md5: 3d3a35463e550d2e098cede57b3977b1
  depends:
  - aws-c-common >=0.9.15,<0.9.16.0a0
  - aws-c-http >=0.8.1,<0.8.2.0a0
  - aws-c-io >=0.14.7,<0.14.8.0a0
  - libgcc-ng >=12
  license: Apache-2.0
  license_family: Apache
  size: 163389
  timestamp: 1714086029743
- kind: conda
  name: aws-c-s3
  version: 0.5.7
  build: h6be9164_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-c-s3-0.5.7-h6be9164_2.conda
  sha256: 5a7c84e3be0dc041fc537a6c5dc9a63a4d7ed535d05bba8a5d49666bc022df89
  md5: 88b24a3a618acd279502f5960442c8a2
  depends:
  - aws-c-auth >=0.7.18,<0.7.19.0a0
  - aws-c-cal >=0.6.11,<0.6.12.0a0
  - aws-c-common >=0.9.15,<0.9.16.0a0
  - aws-c-http >=0.8.1,<0.8.2.0a0
  - aws-c-io >=0.14.7,<0.14.8.0a0
  - aws-checksums >=0.1.18,<0.1.19.0a0
  - libgcc-ng >=12
  - openssl >=3.2.1,<4.0a0
  license: Apache-2.0
  license_family: Apache
  size: 107839
  timestamp: 1713520889154
- kind: conda
  name: aws-c-sdkutils
  version: 0.1.15
  build: hce8ee76_3
  build_number: 3
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-c-sdkutils-0.1.15-hce8ee76_3.conda
  sha256: 72fd73a5de0730997a36bf20ac1cb8cf7c67e40225c280b3dc5e46bc61c7d157
  md5: 0c4f0205a1ae4ca6c89af922ec54271c
  depends:
  - aws-c-common >=0.9.15,<0.9.16.0a0
  - libgcc-ng >=12
  license: Apache-2.0
  license_family: Apache
  size: 55146
  timestamp: 1712145768196
- kind: conda
  name: aws-checksums
  version: 0.1.18
  build: hce8ee76_3
  build_number: 3
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-checksums-0.1.18-hce8ee76_3.conda
  sha256: de0ba47fc8feaaa087d9128e4b5402af72bd46af52b885dee87adfb9e285a816
  md5: 9aa734a17b9b0b793c7696435fe7789a
  depends:
  - aws-c-common >=0.9.15,<0.9.16.0a0
  - libgcc-ng >=12
  license: Apache-2.0
  license_family: Apache
  size: 50068
  timestamp: 1712145648515
- kind: conda
  name: aws-crt-cpp
  version: 0.26.8
  build: h2150271_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-crt-cpp-0.26.8-h2150271_2.conda
  sha256: 61feed2a595c6ceedbebea5150c3b4298fb13a742793e0f2506ef995288a5f27
  md5: 9ee890489734098ce5b3d435b7ec4b80
  depends:
  - aws-c-auth >=0.7.18,<0.7.19.0a0
  - aws-c-cal >=0.6.11,<0.6.12.0a0
  - aws-c-common >=0.9.15,<0.9.16.0a0
  - aws-c-event-stream >=0.4.2,<0.4.3.0a0
  - aws-c-http >=0.8.1,<0.8.2.0a0
  - aws-c-io >=0.14.7,<0.14.8.0a0
  - aws-c-mqtt >=0.10.4,<0.10.5.0a0
  - aws-c-s3 >=0.5.7,<0.5.8.0a0
  - aws-c-sdkutils >=0.1.15,<0.1.16.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: Apache-2.0
  license_family: Apache
  size: 340265
  timestamp: 1714132568550
- kind: conda
  name: aws-sdk-cpp
  version: 1.11.267
  build: hddb5a97_7
  build_number: 7
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/aws-sdk-cpp-1.11.267-hddb5a97_7.conda
  sha256: 1940f4e1e01ae8232092c07d3919496832b36be8ca9f50279b0086b0b5028639
  md5: c6a0616fb788d14efb45ecca46f2f358
  depends:
  - aws-c-common >=0.9.15,<0.9.16.0a0
  - aws-c-event-stream >=0.4.2,<0.4.3.0a0
  - aws-checksums >=0.1.18,<0.1.19.0a0
  - aws-crt-cpp >=0.26.8,<0.26.9.0a0
  - libcurl >=8.7.1,<9.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - openssl >=3.2.1,<4.0a0
  license: Apache-2.0
  license_family: Apache
  size: 3638454
  timestamp: 1713967770190
- kind: pypi
  name: babel
  version: 2.15.0
  url: https://files.pythonhosted.org/packages/27/45/377f7e32a5c93d94cd56542349b34efab5ca3f9e2fd5a68c5e93169aa32d/Babel-2.15.0-py3-none-any.whl
  sha256: 08706bdad8d0a3413266ab61bd6c34d0c28d6e1e7badf40a2cebe67644e2e1fb
  requires_dist:
  - pytz>=2015.7 ; python_version < '3.9'
  - pytest>=6.0 ; extra == 'dev'
  - pytest-cov ; extra == 'dev'
  - freezegun~=1.0 ; extra == 'dev'
  requires_python: '>=3.8'
- kind: pypi
  name: beautifulsoup4
  version: 4.12.3
  url: https://files.pythonhosted.org/packages/b1/fe/e8c672695b37eecc5cbf43e1d0638d88d66ba3a44c4d321c796f4e59167f/beautifulsoup4-4.12.3-py3-none-any.whl
  sha256: b80878c9f40111313e55da8ba20bdba06d8fa3969fc68304167741bbf9e082ed
  requires_dist:
  - soupsieve>1.2
  - cchardet ; extra == 'cchardet'
  - chardet ; extra == 'chardet'
  - charset-normalizer ; extra == 'charset-normalizer'
  - html5lib ; extra == 'html5lib'
  - lxml ; extra == 'lxml'
  requires_python: '>=3.6.0'
- kind: pypi
  name: bidict
  version: 0.23.1
  url: https://files.pythonhosted.org/packages/99/37/e8730c3587a65eb5645d4aba2d27aae48e8003614d6aaf15dda67f702f1f/bidict-0.23.1-py3-none-any.whl
  sha256: 5dae8d4d79b552a71cbabc7deb25dfe8ce710b17ff41711e13010ead2abfc3e5
  requires_python: '>=3.8'
- kind: conda
  name: blas
  version: '1.0'
  build: mkl
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/blas-1.0-mkl.tar.bz2
  sha256: a9a9125029a66905fc9e932dfd4f595be3a59a30db37fd7bf4a675a5c6151d62
  md5: 349aef876b1d8c9dccae01de20d5b385
  depends:
  - mkl
  track_features:
  - blas_mkl
  license: BSD 3-clause
  size: 1381
- kind: pypi
  name: bleach
  version: 6.1.0
  url: https://files.pythonhosted.org/packages/ea/63/da7237f805089ecc28a3f36bca6a21c31fcbc2eb380f3b8f1be3312abd14/bleach-6.1.0-py3-none-any.whl
  sha256: 3225f354cfc436b9789c66c4ee030194bee0568fbf9cbdad3bc8b5c26c5f12b6
  requires_dist:
  - six>=1.9.0
  - webencodings
  - tinycss2<1.3,>=1.1.0 ; extra == 'css'
  requires_python: '>=3.8'
- kind: pypi
  name: blinker
  version: 1.8.2
  url: https://files.pythonhosted.org/packages/bb/2a/10164ed1f31196a2f7f3799368a821765c62851ead0e630ab52b8e14b4d0/blinker-1.8.2-py3-none-any.whl
  sha256: 1779309f71bf239144b9399d06ae925637cf6634cf6bd131104184531bf67c01
  requires_python: '>=3.8'
- kind: conda
  name: brotli-python
  version: 1.1.0
  build: py310hc6cd4ac_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/brotli-python-1.1.0-py310hc6cd4ac_1.conda
  sha256: e22268d81905338570786921b3def88e55f9ed6d0ccdd17d9fbae31a02fbef69
  md5: 1f95722c94f00b69af69a066c7433714
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  constrains:
  - libbrotlicommon 1.1.0 hd590300_1
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/brotli
  size: 349397
  timestamp: 1695990295884
- kind: conda
  name: bzip2
  version: 1.0.8
  build: hd590300_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-hd590300_5.conda
  sha256: 242c0c324507ee172c0e0dd2045814e746bb303d1eb78870d182ceb0abc726a8
  md5: 69b8b6202a07720f448be700e300ccf4
  depends:
  - libgcc-ng >=12
  license: bzip2-1.0.6
  license_family: BSD
  size: 254228
  timestamp: 1699279927352
- kind: conda
  name: c-ares
  version: 1.28.1
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/c-ares-1.28.1-hd590300_0.conda
  sha256: cb25063f3342149c7924b21544109696197a9d774f1407567477d4f3026bf38a
  md5: dcde58ff9a1f30b0037a2315d1846d1f
  depends:
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 168875
  timestamp: 1711819445938
- kind: conda
  name: ca-certificates
  version: 2024.2.2
  build: hbcca054_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/ca-certificates-2024.2.2-hbcca054_0.conda
  sha256: 91d81bfecdbb142c15066df70cc952590ae8991670198f92c66b62019b251aeb
  md5: 2f4327a1cbe7f022401b236e915a5fef
  license: ISC
  size: 155432
  timestamp: 1706843687645
- kind: pypi
  name: cachetools
  version: 5.3.3
  url: https://files.pythonhosted.org/packages/fb/2b/a64c2d25a37aeb921fddb929111413049fc5f8b9a4c1aefaffaafe768d54/cachetools-5.3.3-py3-none-any.whl
  sha256: 0abad1021d3f8325b2fc1d2e9c8b9c9d57b04c3932657a72465447332c24d945
  requires_python: '>=3.7'
- kind: conda
  name: cairo
  version: 1.18.0
  build: h3faef2a_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/cairo-1.18.0-h3faef2a_0.conda
  sha256: 142e2639a5bc0e99c44d76f4cc8dce9c6a2d87330c4beeabb128832cd871a86e
  md5: f907bb958910dc404647326ca80c263e
  depends:
  - fontconfig >=2.14.2,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - icu >=73.2,<74.0a0
  - libgcc-ng >=12
  - libglib >=2.78.0,<3.0a0
  - libpng >=1.6.39,<1.7.0a0
  - libstdcxx-ng >=12
  - libxcb >=1.15,<1.16.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - pixman >=0.42.2,<1.0a0
  - xorg-libice >=1.1.1,<2.0a0
  - xorg-libsm >=1.2.4,<2.0a0
  - xorg-libx11 >=1.8.6,<2.0a0
  - xorg-libxext >=1.3.4,<2.0a0
  - xorg-libxrender >=0.9.11,<0.10.0a0
  - zlib
  license: LGPL-2.1-only or MPL-1.1
  size: 982351
  timestamp: 1697028423052
- kind: conda
  name: ceres-solver
  version: 2.2.0
  build: h30ec75d_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/ceres-solver-2.2.0-h30ec75d_2.conda
  sha256: 8db7b51a75ab27410738b826d4803ae90e8fb445cd614c79129b7b4abfcaabd4
  md5: 3e22f317903149ec5220795a9bc32aad
  depends:
  - eigen
  - gflags >=2.2.2,<2.3.0a0
  - glog >=0.7.0,<0.8.0a0
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libgcc-ng >=12
  - liblapack >=3.9.0,<4.0a0
  - libstdcxx-ng >=12
  - suitesparse >=5.10.1,<6.0a0
  - tbb
  license: BSD-3-Clause
  license_family: BSD
  size: 1463615
  timestamp: 1708286440640
- kind: conda
  name: certifi
  version: 2024.2.2
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/certifi-2024.2.2-pyhd8ed1ab_0.conda
  sha256: f1faca020f988696e6b6ee47c82524c7806380b37cfdd1def32f92c326caca54
  md5: 0876280e409658fc6f9e75d035960333
  depends:
  - python >=3.7
  license: ISC
  purls:
  - pkg:pypi/certifi
  size: 160559
  timestamp: 1707022289175
- kind: pypi
  name: cffi
  version: 1.16.0
  url: https://files.pythonhosted.org/packages/c9/7c/43d81bdd5a915923c3bad5bb4bff401ea00ccc8e28433fb6083d2e3bf58e/cffi-1.16.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: e4108df7fe9b707191e55f33efbcb2d81928e10cea45527879a4749cbe472614
  requires_dist:
  - pycparser
  requires_python: '>=3.8'
- kind: pypi
  name: chardet
  version: 5.2.0
  url: https://files.pythonhosted.org/packages/38/6f/f5fbc992a329ee4e0f288c1fe0e2ad9485ed064cac731ed2fe47dcc38cbf/chardet-5.2.0-py3-none-any.whl
  sha256: e1cf59446890a00105fe7b7912492ea04b6e6f06d4b742b2c788469e34c82970
  requires_python: '>=3.7'
- kind: conda
  name: charset-normalizer
  version: 3.3.2
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.3.2-pyhd8ed1ab_0.conda
  sha256: 20cae47d31fdd58d99c4d2e65fbdcefa0b0de0c84e455ba9d6356a4bdbc4b5b9
  md5: 7f4a9e3fcff3f6356ae99244a014da6a
  depends:
  - python >=3.7
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/charset-normalizer
  size: 46597
  timestamp: 1698833765762
- kind: pypi
  name: click
  version: 8.1.7
  url: https://files.pythonhosted.org/packages/00/2e/d53fa4befbf2cfa713304affc7ca780ce4fc1fd8710527771b58311a3229/click-8.1.7-py3-none-any.whl
  sha256: ae74fb96c20a0277a1d615f1e4d73c8414f5a98db8b799a7931d1582f3390c28
  requires_dist:
  - colorama ; platform_system == 'Windows'
  - importlib-metadata ; python_version < '3.8'
  requires_python: '>=3.7'
- kind: conda
  name: colmap
  version: 3.9.1
  build: cpuhe398016_3
  build_number: 3
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/colmap-3.9.1-cpuhe398016_3.conda
  sha256: 7affed44ed46eba539ab08b339a2cdf068044476eda544825ff169145b682a6d
  md5: 5acfefed4bc8fa569fa198437817f18b
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  - ceres-solver >=2.2.0,<2.3.0a0
  - eigen >=3.4.0,<3.4.1.0a0
  - flann >=1.9.2,<1.9.3.0a0
  - freeimage >=3.18.0,<3.19.0a0
  - gflags >=2.2.2,<2.3.0a0
  - glew >=2.1.0,<2.2.0a0
  - glog >=0.7.0,<0.8.0a0
  - gmp >=6.3.0,<7.0a0
  - libblas >=3.9.0,<4.0a0
  - libboost >=1.84.0,<1.85.0a0
  - libcblas >=3.9.0,<4.0a0
  - libgcc-ng >=12
  - libglu
  - libsqlite >=3.45.2,<4.0a0
  - libstdcxx-ng >=12
  - libxcb >=1.15,<1.16.0a0
  - lz4-c >=1.9.3,<1.10.0a0
  - metis >=5.1.0,<5.1.1.0a0
  - qt-main >=5.15.8,<5.16.0a0
  - suitesparse >=5.10.1,<6.0a0
  - vlfeat >=0.9.21,<0.9.22.0a0
  - xorg-libice >=1.1.1,<2.0a0
  - xorg-libsm >=1.2.4,<2.0a0
  - xorg-libx11 >=1.8.7,<2.0a0
  - xorg-libxau >=1.0.11,<2.0a0
  - xorg-libxdmcp
  - xorg-libxext >=1.3.4,<2.0a0
  - xorg-libxfixes
  - xorg-libxrender >=0.9.11,<0.10.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 6321163
  timestamp: 1711680295114
- kind: pypi
  name: colorama
  version: 0.4.6
  url: https://files.pythonhosted.org/packages/d1/d6/3965ed04c63042e047cb6a3e6ed1a63a35087b6a609aa3a15ed8ac56c221/colorama-0.4.6-py2.py3-none-any.whl
  sha256: 4f1d9991f5acc0ca119f9d443620b77f9d6b33703e51011c16baf57afb285fc6
  requires_python: '!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*,!=3.5.*,!=3.6.*,>=2.7'
- kind: pypi
  name: colorlog
  version: 6.8.2
  url: https://files.pythonhosted.org/packages/f3/18/3e867ab37a24fdf073c1617b9c7830e06ec270b1ea4694a624038fc40a03/colorlog-6.8.2-py3-none-any.whl
  sha256: 4dcbb62368e2800cb3c5abd348da7e53f6c362dda502ec27c560b2e58a66bd33
  requires_dist:
  - colorama ; sys_platform == 'win32'
  - black ; extra == 'development'
  - flake8 ; extra == 'development'
  - mypy ; extra == 'development'
  - pytest ; extra == 'development'
  - types-colorama ; extra == 'development'
  requires_python: '>=3.6'
- kind: pypi
  name: comet-ml
  version: 3.41.0
  url: https://files.pythonhosted.org/packages/67/38/34ca2481c857c704ce2c44a78df31eed98b05558c87be097a1eb430ec332/comet_ml-3.41.0-py3-none-any.whl
  sha256: bdbb4ab0097567eed9d89a9e14372d7623993a64b330adc6c74a0ea9de379167
  requires_dist:
  - everett[ini]<3.2.0,>=1.0.1
  - jsonschema!=3.1.0,>=2.6.0
  - psutil>=5.6.3
  - python-box<7.0.0
  - requests-toolbelt>=0.8.0
  - requests>=2.18.4
  - semantic-version>=2.8.0
  - sentry-sdk>=1.1.0
  - simplejson
  - six
  - urllib3>=1.21.1
  - websocket-client<1.4.0,>=0.55.0
  - wrapt>=1.11.2
  - wurlitzer>=1.0.2
  - comet-git-pure>=0.19.11 ; python_version < '3.0'
  - importlib-metadata ; python_version < '3.8'
  - dulwich!=0.20.33,>=0.20.6 ; python_version >= '3.0'
  - setuptools ; python_version >= '3.12'
  - rich>=13.3.2 ; python_version >= '3.7.0'
  requires_python: '>=3.6'
- kind: pypi
  name: comm
  version: 0.2.2
  url: https://files.pythonhosted.org/packages/e6/75/49e5bfe642f71f272236b5b2d2691cf915a7283cc0ceda56357b61daa538/comm-0.2.2-py3-none-any.whl
  sha256: e6fb86cb70ff661ee8c9c14e7d36d6de3b4066f1441be4063df9c5009f0a64d3
  requires_dist:
  - traitlets>=4
  - pytest ; extra == 'test'
  requires_python: '>=3.8'
- kind: pypi
  name: configargparse
  version: 1.7.1
  url: https://files.pythonhosted.org/packages/31/28/d28211d29bcc3620b1fece85a65ce5bb22f18670a03cd28ea4b75ede270c/configargparse-1.7.1-py3-none-any.whl
  sha256: 8b586a31f9d873abd1ca527ffbe58863c99f36d896e2829779803125e83be4b6
  requires_dist:
  - pyyaml ; extra == 'yaml'
  - mock ; extra == 'test'
  - pyyaml ; extra == 'test'
  - pytest ; extra == 'test'
  requires_python: '>=3.6'
- kind: pypi
  name: configobj
  version: 5.0.8
  url: https://files.pythonhosted.org/packages/d3/bb/d10e531b297dd1d46f6b1fd11d018247af9f2d460037554bb7bb9011c6ac/configobj-5.0.8-py2.py3-none-any.whl
  sha256: a7a8c6ab7daade85c3f329931a807c8aee750a2494363934f8ea84d8a54c87ea
  requires_dist:
  - six
  requires_python: '>=2.7,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*'
- kind: pypi
  name: contourpy
  version: 1.2.1
  url: https://files.pythonhosted.org/packages/67/0f/6e5b4879594cd1cbb6a2754d9230937be444f404cf07c360c07a10b36aac/contourpy-1.2.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: 39f3ecaf76cd98e802f094e0d4fbc6dc9c45a8d0c4d185f0f6c2234e14e5f75b
  requires_dist:
  - numpy>=1.20
  - furo ; extra == 'docs'
  - sphinx>=7.2 ; extra == 'docs'
  - sphinx-copybutton ; extra == 'docs'
  - bokeh ; extra == 'bokeh'
  - selenium ; extra == 'bokeh'
  - contourpy[bokeh,docs] ; extra == 'mypy'
  - docutils-stubs ; extra == 'mypy'
  - mypy==1.8.0 ; extra == 'mypy'
  - types-pillow ; extra == 'mypy'
  - contourpy[test-no-images] ; extra == 'test'
  - matplotlib ; extra == 'test'
  - pillow ; extra == 'test'
  - pytest ; extra == 'test-no-images'
  - pytest-cov ; extra == 'test-no-images'
  - pytest-xdist ; extra == 'test-no-images'
  - wurlitzer ; extra == 'test-no-images'
  requires_python: '>=3.9'
- kind: pypi
  name: cryptography
  version: 42.0.7
  url: https://files.pythonhosted.org/packages/7b/4e/fa4896744259ee8602464ed2c7330b736cc4dd3fd92f63cd56828bf36707/cryptography-42.0.7-cp39-abi3-manylinux_2_28_x86_64.whl
  sha256: efd0bf5205240182e0f13bcaea41be4fdf5c22c5129fc7ced4a0282ac86998c9
  requires_dist:
  - cffi>=1.12 ; platform_python_implementation != 'PyPy'
  - sphinx>=5.3.0 ; extra == 'docs'
  - sphinx-rtd-theme>=1.1.1 ; extra == 'docs'
  - pyenchant>=1.6.11 ; extra == 'docstest'
  - readme-renderer ; extra == 'docstest'
  - sphinxcontrib-spelling>=4.0.1 ; extra == 'docstest'
  - nox ; extra == 'nox'
  - ruff ; extra == 'pep8test'
  - mypy ; extra == 'pep8test'
  - check-sdist ; extra == 'pep8test'
  - click ; extra == 'pep8test'
  - build ; extra == 'sdist'
  - bcrypt>=3.1.5 ; extra == 'ssh'
  - pytest>=6.2.0 ; extra == 'test'
  - pytest-benchmark ; extra == 'test'
  - pytest-cov ; extra == 'test'
  - pytest-xdist ; extra == 'test'
  - pretend ; extra == 'test'
  - certifi ; extra == 'test'
  - pytest-randomly ; extra == 'test-randomorder'
  requires_python: '>=3.7'
- kind: conda
  name: cuda
  version: 11.8.0
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-11.8.0-0.tar.bz2
  md5: f274f527ae9d4393e93768c33d8e0058
  depends:
  - cuda-demo-suite >=11.8.86
  - cuda-runtime >=11.8.0
  - cuda-toolkit >=11.8.0
  arch: x86_64
  platform: linux
  size: 1449
  timestamp: 1664475582997
- kind: conda
  name: cuda-cccl
  version: 11.8.89
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-cccl-11.8.89-0.tar.bz2
  md5: 9c48a93deca9c86ab9393c717b55c89d
  arch: x86_64
  platform: linux
  size: 1232088
  timestamp: 1663786133781
- kind: conda
  name: cuda-command-line-tools
  version: 11.8.0
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-command-line-tools-11.8.0-0.tar.bz2
  md5: b782d1e340ffbb8940633bf57e38ea91
  depends:
  - cuda-cupti >=11.8.87
  - cuda-gdb >=11.8.86
  - cuda-memcheck >=11.8.86
  - cuda-nvdisasm >=11.8.86
  - cuda-nvprof >=11.8.87
  - cuda-nvtx >=11.8.86
  - cuda-sanitizer-api >=11.8.86
  arch: x86_64
  platform: linux
  size: 1503
  timestamp: 1664475471680
- kind: conda
  name: cuda-compiler
  version: 11.8.0
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-compiler-11.8.0-0.tar.bz2
  md5: 37f4f099ae0801d57c8547c0f3af6b8f
  depends:
  - cuda-cuobjdump >=11.8.86
  - cuda-cuxxfilt >=11.8.86
  - cuda-nvcc >=11.8.89
  - cuda-nvprune >=11.8.86
  arch: x86_64
  platform: linux
  size: 1464
  timestamp: 1664475480949
- kind: conda
  name: cuda-cudart
  version: 11.8.89
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-cudart-11.8.89-0.tar.bz2
  md5: b68c7ef3eda01e95d5903fb508c5e440
  arch: x86_64
  platform: linux
  size: 201959
  timestamp: 1663787236799
- kind: conda
  name: cuda-cudart-dev
  version: 11.8.89
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-cudart-dev-11.8.89-0.tar.bz2
  md5: fda2522594365c493121c517d3cc5a29
  depends:
  - cuda-cccl
  - cuda-cudart >=11.8.89
  arch: x86_64
  platform: linux
  size: 1137073
  timestamp: 1663787238388
- kind: conda
  name: cuda-cuobjdump
  version: 11.8.86
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-cuobjdump-11.8.86-0.tar.bz2
  md5: 9983898404071b86e93f5411e9ce73d5
  arch: x86_64
  platform: linux
  size: 234042
  timestamp: 1661544389189
- kind: conda
  name: cuda-cupti
  version: 11.8.87
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-cupti-11.8.87-0.tar.bz2
  md5: 2f4b4933285400137cf029fef9a7daa6
  arch: x86_64
  platform: linux
  size: 26508245
  timestamp: 1661562396866
- kind: conda
  name: cuda-cuxxfilt
  version: 11.8.86
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-cuxxfilt-11.8.86-0.tar.bz2
  md5: d5e2aa288a115bf8ed23116987b56a9a
  arch: x86_64
  platform: linux
  size: 298326
  timestamp: 1661507924741
- kind: conda
  name: cuda-demo-suite
  version: 11.8.86
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-demo-suite-11.8.86-0.tar.bz2
  md5: 07bdbd4de564326cc7c7677df92d5657
  arch: x86_64
  platform: linux
  size: 5212691
  timestamp: 1661484990989
- kind: conda
  name: cuda-documentation
  version: 11.8.86
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-documentation-11.8.86-0.tar.bz2
  md5: f3d58829354915696a6cde600382684d
  arch: x86_64
  platform: linux
  size: 90887
  timestamp: 1661488811822
- kind: conda
  name: cuda-driver-dev
  version: 11.8.89
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-driver-dev-11.8.89-0.tar.bz2
  md5: afd39b647ee4eca8a36dc996da4915c4
  arch: x86_64
  platform: linux
  size: 16277
  timestamp: 1663787237700
- kind: conda
  name: cuda-gdb
  version: 11.8.86
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-gdb-11.8.86-0.tar.bz2
  md5: 0190089d9f2d7f16d2daadbbba38b678
  arch: x86_64
  platform: linux
  size: 5013885
  timestamp: 1661489579522
- kind: conda
  name: cuda-libraries
  version: 11.8.0
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-libraries-11.8.0-0.tar.bz2
  md5: 3a43d100104e52ac8209a834c82ab231
  depends:
  - cuda-cudart >=11.8.89
  - cuda-nvrtc >=11.8.89
  - libcublas >=*********
  - libcufft >=*********
  - libcufile >=********
  - libcurand >=10.3.0.86
  - libcusolver >=*********
  - libcusparse >=*********
  - libnpp >=*********
  - libnvjpeg >=*********
  arch: x86_64
  platform: linux
  size: 1535
  timestamp: 1664475490383
- kind: conda
  name: cuda-libraries-dev
  version: 11.8.0
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-libraries-dev-11.8.0-0.tar.bz2
  md5: 5428619702b20a8353bff9f3b152bb2f
  depends:
  - cuda-cccl >=11.8.89
  - cuda-cudart-dev >=11.8.89
  - cuda-driver-dev >=11.8.89
  - cuda-nvrtc-dev >=11.8.89
  - cuda-profiler-api >=11.8.86
  - libcublas-dev >=*********
  - libcufft-dev >=*********
  - libcufile-dev >=********
  - libcurand-dev >=10.3.0.86
  - libcusolver-dev >=*********
  - libcusparse-dev >=*********
  - libnpp-dev >=*********
  - libnvjpeg-dev >=*********
  arch: x86_64
  platform: linux
  size: 1571
  timestamp: 1664475500133
- kind: conda
  name: cuda-memcheck
  version: 11.8.86
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-memcheck-11.8.86-0.tar.bz2
  md5: 0d658b7c0e0799af3a981eff1c33771c
  arch: x86_64
  platform: linux
  size: 171911
  timestamp: 1661470265954
- kind: conda
  name: cuda-nsight
  version: 11.8.86
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-nsight-11.8.86-0.tar.bz2
  md5: f6395abb8428ada738738fd558eba798
  arch: x86_64
  platform: linux
  size: 119143043
  timestamp: 1661544543317
- kind: conda
  name: cuda-nsight-compute
  version: 11.8.0
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-nsight-compute-11.8.0-0.tar.bz2
  md5: 2321698a7ca3b20cc8a5fdcdcd9f8aae
  depends:
  - nsight-compute >=2022.3.0.22
  arch: x86_64
  platform: linux
  size: 1448
  timestamp: 1664475509308
- kind: conda
  name: cuda-nvcc
  version: 11.8.89
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-nvcc-11.8.89-0.tar.bz2
  md5: 33f9af6dac16b94a7dc1111a18fd19bf
  arch: x86_64
  platform: linux
  size: 53254905
  timestamp: 1663787076437
- kind: conda
  name: cuda-nvdisasm
  version: 11.8.86
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-nvdisasm-11.8.86-0.tar.bz2
  md5: 47de8e90d674c06001a82c1e1502532a
  arch: x86_64
  platform: linux
  size: 51087785
  timestamp: 1661489072164
- kind: conda
  name: cuda-nvml-dev
  version: 11.8.86
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-nvml-dev-11.8.86-0.tar.bz2
  md5: 0dc7aaa44f459088c5286801d76034c5
  arch: x86_64
  platform: linux
  size: 84882
  timestamp: 1661543572982
- kind: conda
  name: cuda-nvprof
  version: 11.8.87
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-nvprof-11.8.87-0.tar.bz2
  md5: a58bfa5a7332b1b71ed88d555a2b934a
  arch: x86_64
  platform: linux
  size: 4569849
  timestamp: 1661563457664
- kind: conda
  name: cuda-nvprune
  version: 11.8.86
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-nvprune-11.8.86-0.tar.bz2
  md5: 6f1478d39023065741930e23580c9f75
  arch: x86_64
  platform: linux
  size: 66127
  timestamp: 1661489036289
- kind: conda
  name: cuda-nvrtc
  version: 11.8.89
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-nvrtc-11.8.89-0.tar.bz2
  md5: f4af75ee32661708c979630cdb8f4987
  arch: x86_64
  platform: linux
  size: 20069921
  timestamp: 1663786884512
- kind: conda
  name: cuda-nvrtc-dev
  version: 11.8.89
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-nvrtc-dev-11.8.89-0.tar.bz2
  md5: 953e857784524bf97f58a478cb05f2ac
  depends:
  - cuda-nvrtc >=11.8.89
  arch: x86_64
  platform: linux
  size: 17858592
  timestamp: 1663786894532
- kind: conda
  name: cuda-nvtx
  version: 11.8.86
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-nvtx-11.8.86-0.tar.bz2
  md5: 1825ffc3feb608f2752073935e90bb49
  arch: x86_64
  platform: linux
  size: 58436
  timestamp: 1661544565925
- kind: conda
  name: cuda-nvvp
  version: 11.8.87
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-nvvp-11.8.87-0.tar.bz2
  md5: e9df05575a736ab3c8f89853bdac736a
  depends:
  - cuda-nvdisasm
  - cuda-nvprof
  arch: x86_64
  platform: linux
  size: 119905249
  timestamp: 1661563876321
- kind: conda
  name: cuda-profiler-api
  version: 11.8.86
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-profiler-api-11.8.86-0.tar.bz2
  md5: a41d5bd148ab7f9e7cc81bed84349d5f
  arch: x86_64
  platform: linux
  size: 18856
  timestamp: 1661486685944
- kind: conda
  name: cuda-runtime
  version: 11.8.0
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-runtime-11.8.0-0.tar.bz2
  md5: 3ca379d762f8d7bd727df9e2c9b30664
  depends:
  - cuda-libraries >=11.8.0
  arch: x86_64
  platform: linux
  size: 1434
  timestamp: 1664475536765
- kind: conda
  name: cuda-sanitizer-api
  version: 11.8.86
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-sanitizer-api-11.8.86-0.tar.bz2
  md5: f0f85d83cfb619d64e4fb3d00bd6b52a
  arch: x86_64
  platform: linux
  size: 17426071
  timestamp: 1661489510330
- kind: conda
  name: cuda-toolkit
  version: 11.8.0
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-toolkit-11.8.0-0.tar.bz2
  md5: c8daa030f9548d834f5c61b401f262fc
  depends:
  - cuda-compiler >=11.8.0
  - cuda-documentation >=11.8.86
  - cuda-libraries >=11.8.0
  - cuda-libraries-dev >=11.8.0
  - cuda-nvml-dev >=11.8.86
  - cuda-tools >=11.8.0
  arch: x86_64
  platform: linux
  size: 1481
  timestamp: 1664475564507
- kind: conda
  name: cuda-tools
  version: 11.8.0
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-tools-11.8.0-0.tar.bz2
  md5: 75bb667ae9c91a2d37ee97d3f99cbae2
  depends:
  - cuda-command-line-tools >=11.8.0
  - cuda-visual-tools >=11.8.0
  - gds-tools >=********
  arch: x86_64
  platform: linux
  size: 1454
  timestamp: 1664475555196
- kind: conda
  name: cuda-visual-tools
  version: 11.8.0
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/cuda-visual-tools-11.8.0-0.tar.bz2
  md5: 4dff31d4235b8b13870ede083de222be
  depends:
  - cuda-libraries-dev >=11.8.0
  - cuda-nsight >=11.8.86
  - cuda-nsight-compute >=11.8.0
  - cuda-nvml-dev >=11.8.86
  - cuda-nvvp >=11.8.87
  arch: x86_64
  platform: linux
  size: 1476
  timestamp: 1664475546019
- kind: pypi
  name: cycler
  version: 0.12.1
  url: https://files.pythonhosted.org/packages/e7/05/c19819d5e3d95294a6f5947fb9b9629efb316b96de511b418c53d245aae6/cycler-0.12.1-py3-none-any.whl
  sha256: 85cef7cff222d8644161529808465972e51340599459b8ac3ccbac5a854e0d30
  requires_dist:
  - ipython ; extra == 'docs'
  - matplotlib ; extra == 'docs'
  - numpydoc ; extra == 'docs'
  - sphinx ; extra == 'docs'
  - pytest ; extra == 'tests'
  - pytest-cov ; extra == 'tests'
  - pytest-xdist ; extra == 'tests'
  requires_python: '>=3.8'
- kind: pypi
  name: dash
  version: 2.17.0
  url: https://files.pythonhosted.org/packages/63/22/bc266b167111e70a2da940e78b78d22fea5b1ee32b512ed0789bd6cc2a9f/dash-2.17.0-py3-none-any.whl
  sha256: 2421569023b2cd46ea2d4b2c14fe72c71b7436527a3102219b2265fa361e7c67
  requires_dist:
  - flask<3.1,>=1.0.4
  - werkzeug<3.1
  - plotly>=5.0.0
  - dash-html-components==2.0.0
  - dash-core-components==2.0.0
  - dash-table==5.0.0
  - importlib-metadata
  - typing-extensions>=4.1.1
  - requests
  - retrying
  - nest-asyncio
  - setuptools
  - redis>=3.5.3 ; extra == 'celery'
  - celery[redis]>=5.1.2 ; extra == 'celery'
  - black==22.3.0 ; extra == 'ci'
  - dash-flow-example==0.0.5 ; extra == 'ci'
  - dash-dangerously-set-inner-html ; extra == 'ci'
  - flake8==7.0.0 ; extra == 'ci'
  - flaky==3.8.1 ; extra == 'ci'
  - flask-talisman==1.0.0 ; extra == 'ci'
  - mimesis<=11.1.0 ; extra == 'ci'
  - mock==4.0.3 ; extra == 'ci'
  - numpy<=1.26.3 ; extra == 'ci'
  - orjson==3.9.12 ; extra == 'ci'
  - openpyxl ; extra == 'ci'
  - pandas>=1.4.0 ; extra == 'ci'
  - pyarrow ; extra == 'ci'
  - pylint==3.0.3 ; extra == 'ci'
  - pytest-mock ; extra == 'ci'
  - pytest-sugar==0.9.6 ; extra == 'ci'
  - pyzmq==25.1.2 ; extra == 'ci'
  - xlrd>=2.0.1 ; extra == 'ci'
  - pytest-rerunfailures ; extra == 'ci'
  - jupyterlab<4.0.0 ; extra == 'ci'
  - flask-compress ; extra == 'compress'
  - coloredlogs>=15.0.1 ; extra == 'dev'
  - fire>=0.4.0 ; extra == 'dev'
  - pyyaml>=5.4.1 ; extra == 'dev'
  - diskcache>=5.2.1 ; extra == 'diskcache'
  - multiprocess>=0.70.12 ; extra == 'diskcache'
  - psutil>=5.8.0 ; extra == 'diskcache'
  - beautifulsoup4>=4.8.2 ; extra == 'testing'
  - lxml>=4.6.2 ; extra == 'testing'
  - percy>=2.0.2 ; extra == 'testing'
  - pytest>=6.0.2 ; extra == 'testing'
  - requests[security]>=2.21.0 ; extra == 'testing'
  - selenium<=4.2.0,>=3.141.0 ; extra == 'testing'
  - waitress>=1.4.4 ; extra == 'testing'
  - multiprocess>=0.70.12 ; extra == 'testing'
  - psutil>=5.8.0 ; extra == 'testing'
  - dash-testing-stub>=0.0.2 ; extra == 'testing'
  - cryptography<3.4 ; python_version < '3.7' and extra == 'testing'
  requires_python: '>=3.8'
- kind: pypi
  name: dash-core-components
  version: 2.0.0
  url: https://files.pythonhosted.org/packages/00/9e/a29f726e84e531a36d56cff187e61d8c96d2cc253c5bcef9a7695acb7e6a/dash_core_components-2.0.0-py3-none-any.whl
  sha256: 52b8e8cce13b18d0802ee3acbc5e888cb1248a04968f962d63d070400af2e346
- kind: pypi
  name: dash-html-components
  version: 2.0.0
  url: https://files.pythonhosted.org/packages/75/65/1b16b853844ef59b2742a7de74a598f376ac0ab581f0dcc34db294e5c90e/dash_html_components-2.0.0-py3-none-any.whl
  sha256: b42cc903713c9706af03b3f2548bda4be7307a7cf89b7d6eae3da872717d1b63
- kind: pypi
  name: dash-table
  version: 5.0.0
  url: https://files.pythonhosted.org/packages/da/ce/43f77dc8e7bbad02a9f88d07bf794eaf68359df756a28bb9f2f78e255bb1/dash_table-5.0.0-py3-none-any.whl
  sha256: 19036fa352bb1c11baf38068ec62d172f0515f73ca3276c79dee49b95ddc16c9
- kind: conda
  name: dav1d
  version: 1.2.1
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/dav1d-1.2.1-hd590300_0.conda
  sha256: 22053a5842ca8ee1cf8e1a817138cdb5e647eb2c46979f84153f6ad7bde73020
  md5: 418c6ca5929a611cbd69204907a83995
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 760229
  timestamp: 1685695754230
- kind: conda
  name: dbus
  version: 1.13.6
  build: h5008d03_3
  build_number: 3
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.13.6-h5008d03_3.tar.bz2
  sha256: 8f5f995699a2d9dbdd62c61385bfeeb57c82a681a7c8c5313c395aa0ccab68a5
  md5: ecfff944ba3960ecb334b9a2663d708d
  depends:
  - expat >=2.4.2,<3.0a0
  - libgcc-ng >=9.4.0
  - libglib >=2.70.2,<3.0a0
  license: GPL-2.0-or-later
  license_family: GPL
  size: 618596
  timestamp: 1640112124844
- kind: pypi
  name: debugpy
  version: 1.8.1
  url: https://files.pythonhosted.org/packages/7a/27/78d5cf9c7aba43f8341e78273ab776913d2d33beb581ec39b65e56a0db77/debugpy-1.8.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: dda73bf69ea479c8577a0448f8c707691152e6c4de7f0c4dec5a4bc11dee516e
  requires_python: '>=3.8'
- kind: pypi
  name: decorator
  version: 5.1.1
  url: https://files.pythonhosted.org/packages/d5/50/83c593b07763e1161326b3b8c6686f0f4b0f24d5526546bee538c89837d6/decorator-5.1.1-py3-none-any.whl
  sha256: b8c3f85900b9dc423225913c5aace94729fe1fa9763b38939a95226f02d37186
  requires_python: '>=3.5'
- kind: pypi
  name: defusedxml
  version: 0.7.1
  url: https://files.pythonhosted.org/packages/07/6c/aa3f2f849e01cb6a001cd8554a88d4c77c5c1a31c95bdf1cf9301e6d9ef4/defusedxml-0.7.1-py2.py3-none-any.whl
  sha256: a352e7e428770286cc899e2542b6cdaedb2b4953ff269a210103ec58f6198a61
  requires_python: '>=2.7,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*'
- kind: pypi
  name: descartes
  version: 1.1.0
  url: https://files.pythonhosted.org/packages/e5/b6/1ed2eb03989ae574584664985367ba70cd9cf8b32ee8cad0e8aaeac819f3/descartes-1.1.0-py3-none-any.whl
  sha256: 4c62dc41109689d03e4b35de0a2bcbdeeb81047badc607c4415d5c753bd683af
  requires_dist:
  - matplotlib
- kind: pypi
  name: dill
  version: 0.3.8
  url: https://files.pythonhosted.org/packages/c9/7a/cef76fd8438a42f96db64ddaa85280485a9c395e7df3db8158cfec1eee34/dill-0.3.8-py3-none-any.whl
  sha256: c36ca9ffb54365bdd2f8eb3eff7d2a21237f8452b57ace88b1ac615b7e815bd7
  requires_dist:
  - objgraph>=1.7.2 ; extra == 'graph'
  - gprof2dot>=2022.7.29 ; extra == 'profile'
  requires_python: '>=3.8'
- kind: pypi
  name: docker-pycreds
  version: 0.4.0
  url: https://files.pythonhosted.org/packages/f5/e8/f6bd1eee09314e7e6dee49cbe2c5e22314ccdb38db16c9fc72d2fa80d054/docker_pycreds-0.4.0-py2.py3-none-any.whl
  sha256: 7266112468627868005106ec19cd0d722702d2b7d5912a28e19b826c3d37af49
  requires_dist:
  - six>=1.4.0
- kind: pypi
  name: docstring-parser
  version: '0.16'
  url: https://files.pythonhosted.org/packages/d5/7c/e9fcff7623954d86bdc17782036cbf715ecab1bec4847c008557affe1ca8/docstring_parser-0.16-py3-none-any.whl
  sha256: bf0a1387354d3691d102edef7ec124f219ef639982d096e26e3b60aeffa90637
  requires_python: '>=3.6,<4.0'
- kind: pypi
  name: dulwich
  version: 0.22.1
  url: https://files.pythonhosted.org/packages/62/45/c663da77e3799057e561432b74967f3348cc985a2bdab433a7b01106611c/dulwich-0.22.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: b03092399f0f5d3e112405b890128afdb9e1f203eafb812f5d9105b0f5fac9d4
  requires_dist:
  - urllib3>=1.25
  - typing-extensions ; python_version <= '3.7'
  - setuptools ; python_version >= '3.12'
  - fastimport ; extra == 'fastimport'
  - urllib3>=1.24.1 ; extra == 'https'
  - paramiko ; extra == 'paramiko'
  - gpg ; extra == 'pgp'
  requires_python: '>=3.7'
- kind: conda
  name: eigen
  version: 3.4.0
  build: h00ab1b0_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/eigen-3.4.0-h00ab1b0_0.conda
  sha256: 53b15a98aadbe0704479bacaf7a5618fcb32d1577be320630674574241639b34
  md5: b1b879d6d093f55dd40d58b5eb2f0699
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MPL-2.0
  license_family: MOZILLA
  size: 1088433
  timestamp: 1690272126173
- kind: pypi
  name: embreex
  version: 2.17.7.post4
  url: https://files.pythonhosted.org/packages/90/c7/f4abf09c6109c975e462c72ea4f9e2d72d78e20325dafb151c8e9789a31d/embreex-2.17.7.post4-cp310-cp310-manylinux_2_28_x86_64.whl
  sha256: 0750fba99a77049baff6b25c10206d328ff385ee2e35d6147423b0bba50c904c
  requires_dist:
  - numpy ; python_version < '3.12'
  - numpy>=1.26.0b1 ; python_version >= '3.12'
- kind: pypi
  name: everett
  version: 3.1.0
  url: https://files.pythonhosted.org/packages/91/9a/d882fd7562208456236fb2e62b762bf16fbc9ecde842bb871f676ca0f7e1/everett-3.1.0-py2.py3-none-any.whl
  sha256: db13891b849e45e54faea93ee79881d12458c5378f5b9b7f806eeff03ce1de3c
  requires_dist:
  - configobj ; extra == 'ini'
  - pyyaml ; extra == 'yaml'
- kind: pypi
  name: exceptiongroup
  version: 1.2.1
  url: https://files.pythonhosted.org/packages/01/90/79fe92dd413a9cab314ef5c591b5aa9b9ba787ae4cadab75055b0ae00b33/exceptiongroup-1.2.1-py3-none-any.whl
  sha256: 5258b9ed329c5bbdd31a309f53cbfb0b155341807f6ff7606a1e801a891b29ad
  requires_dist:
  - pytest>=6 ; extra == 'test'
  requires_python: '>=3.7'
- kind: pypi
  name: executing
  version: 2.0.1
  url: https://files.pythonhosted.org/packages/80/03/6ea8b1b2a5ab40a7a60dc464d3daa7aa546e0a74d74a9f8ff551ea7905db/executing-2.0.1-py2.py3-none-any.whl
  sha256: eac49ca94516ccc753f9fb5ce82603156e590b27525a8bc32cce8ae302eb61bc
  requires_dist:
  - asttokens>=2.1.0 ; extra == 'tests'
  - ipython ; extra == 'tests'
  - pytest ; extra == 'tests'
  - coverage ; extra == 'tests'
  - coverage-enable-subprocess ; extra == 'tests'
  - littleutils ; extra == 'tests'
  - rich ; python_version >= '3.11' and extra == 'tests'
  requires_python: '>=3.5'
- kind: conda
  name: expat
  version: 2.6.2
  build: h59595ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/expat-2.6.2-h59595ed_0.conda
  sha256: 89916c536ae5b85bb8bf0cfa27d751e274ea0911f04e4a928744735c14ef5155
  md5: 53fb86322bdb89496d7579fe3f02fd61
  depends:
  - libexpat 2.6.2 h59595ed_0
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 137627
  timestamp: 1710362144873
- kind: pypi
  name: fastjsonschema
  version: 2.19.1
  url: https://files.pythonhosted.org/packages/9c/b9/79691036d4a8f9857e74d1728b23f34f583b81350a27492edda58d5604e1/fastjsonschema-2.19.1-py3-none-any.whl
  sha256: 3672b47bc94178c9f23dbb654bf47440155d4db9df5f7bc47643315f9c405cd0
  requires_dist:
  - colorama ; extra == 'devel'
  - jsonschema ; extra == 'devel'
  - json-spec ; extra == 'devel'
  - pylint ; extra == 'devel'
  - pytest ; extra == 'devel'
  - pytest-benchmark ; extra == 'devel'
  - pytest-cache ; extra == 'devel'
  - validictory ; extra == 'devel'
- kind: conda
  name: ffmpeg
  version: 7.0.0
  build: gpl_hdd1146e_100
  build_number: 100
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/ffmpeg-7.0.0-gpl_hdd1146e_100.conda
  sha256: 39a457f10212176e161ecd7ea4c182566afe6922576dc2c7609a87c730c2451d
  md5: 0d4bb500ba9958dacb59c7e7e6b5abe0
  depends:
  - __glibc >=2.17,<3.0.a0
  - aom >=3.9.0,<3.10.0a0
  - bzip2 >=1.0.8,<2.0a0
  - dav1d >=1.2.1,<1.2.2.0a0
  - fontconfig >=2.14.2,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - gmp >=6.3.0,<7.0a0
  - gnutls >=3.7.9,<3.8.0a0
  - harfbuzz >=8.4.0,<9.0a0
  - lame >=3.100,<3.101.0a0
  - libass >=0.17.1,<0.17.2.0a0
  - libgcc-ng >=12
  - libiconv >=1.17,<2.0a0
  - libopenvino >=2024.0.0,<2024.0.1.0a0
  - libopenvino-auto-batch-plugin >=2024.0.0,<2024.0.1.0a0
  - libopenvino-auto-plugin >=2024.0.0,<2024.0.1.0a0
  - libopenvino-hetero-plugin >=2024.0.0,<2024.0.1.0a0
  - libopenvino-intel-cpu-plugin >=2024.0.0,<2024.0.1.0a0
  - libopenvino-intel-gpu-plugin >=2024.0.0,<2024.0.1.0a0
  - libopenvino-ir-frontend >=2024.0.0,<2024.0.1.0a0
  - libopenvino-onnx-frontend >=2024.0.0,<2024.0.1.0a0
  - libopenvino-paddle-frontend >=2024.0.0,<2024.0.1.0a0
  - libopenvino-pytorch-frontend >=2024.0.0,<2024.0.1.0a0
  - libopenvino-tensorflow-frontend >=2024.0.0,<2024.0.1.0a0
  - libopenvino-tensorflow-lite-frontend >=2024.0.0,<2024.0.1.0a0
  - libopus >=1.3.1,<2.0a0
  - libstdcxx-ng >=12
  - libva >=2.21.0,<3.0a0
  - libvpx >=1.14.0,<1.15.0a0
  - libxcb >=1.15,<1.16.0a0
  - libxml2 >=2.12.6,<3.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - openh264 >=2.4.1,<2.4.2.0a0
  - svt-av1 >=2.0.0,<2.0.1.0a0
  - x264 >=1!164.3095,<1!165
  - x265 >=3.5,<3.6.0a0
  - xorg-libx11 >=1.8.9,<2.0a0
  - xz >=5.2.6,<6.0a0
  license: GPL-2.0-or-later
  license_family: GPL
  size: 10099146
  timestamp: 1714840178324
- kind: conda
  name: filelock
  version: 3.14.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/filelock-3.14.0-pyhd8ed1ab_0.conda
  sha256: 6031be667e1b0cc0dee713f1cbca887cdee4daafa8bac478da33096f3147d38b
  md5: 831d85ae0acfba31b8efd0f0d07da736
  depends:
  - python >=3.7
  license: Unlicense
  purls:
  - pkg:pypi/filelock
  size: 15902
  timestamp: 1714422911808
- kind: pypi
  name: fire
  version: 0.6.0
  url: https://files.pythonhosted.org/packages/1b/1b/84c63f592ecdfbb3d77d22a8d93c9b92791e4fa35677ad71a7d6449100f8/fire-0.6.0.tar.gz
  sha256: 54ec5b996ecdd3c0309c800324a0703d6da512241bc73b553db959d98de0aa66
  requires_dist:
  - six
  - termcolor
  - enum34 ; python_version < '3.4'
- kind: conda
  name: flann
  version: 1.9.2
  build: h2b5ea80_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/flann-1.9.2-h2b5ea80_0.conda
  sha256: eda6991f3c85b8263fec8fb83fc3d64e3d28963659f1c3f18067487d40d69491
  md5: 0846ae36cd85f5e469b630ee74719ad8
  depends:
  - _openmp_mutex >=4.5
  - hdf5 >=1.14.2,<1.14.4.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - lz4-c >=1.9.3,<1.10.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 1565184
  timestamp: 1697891385570
- kind: pypi
  name: flask
  version: 3.0.3
  url: https://files.pythonhosted.org/packages/61/80/ffe1da13ad9300f87c93af113edd0638c75138c42a0994becfacac078c06/flask-3.0.3-py3-none-any.whl
  sha256: 34e815dfaa43340d1d15a5c3a02b8476004037eb4840b34910c6e21679d288f3
  requires_dist:
  - werkzeug>=3.0.0
  - jinja2>=3.1.2
  - itsdangerous>=2.1.2
  - click>=8.1.3
  - blinker>=1.6.2
  - importlib-metadata>=3.6.0 ; python_version < '3.10'
  - asgiref>=3.2 ; extra == 'async'
  - python-dotenv ; extra == 'dotenv'
  requires_python: '>=3.8'
- kind: conda
  name: font-ttf-dejavu-sans-mono
  version: '2.37'
  build: hab24e00_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
  sha256: 58d7f40d2940dd0a8aa28651239adbf5613254df0f75789919c4e6762054403b
  md5: 0c96522c6bdaed4b1566d11387caaf45
  license: BSD-3-Clause
  license_family: BSD
  size: 397370
  timestamp: 1566932522327
- kind: conda
  name: font-ttf-inconsolata
  version: '3.000'
  build: h77eed37_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
  sha256: c52a29fdac682c20d252facc50f01e7c2e7ceac52aa9817aaf0bb83f7559ec5c
  md5: 34893075a5c9e55cdafac56607368fc6
  license: OFL-1.1
  license_family: Other
  size: 96530
  timestamp: 1620479909603
- kind: conda
  name: font-ttf-source-code-pro
  version: '2.038'
  build: h77eed37_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
  sha256: 00925c8c055a2275614b4d983e1df637245e19058d79fc7dd1a93b8d9fb4b139
  md5: 4d59c254e01d9cde7957100457e2d5fb
  license: OFL-1.1
  license_family: Other
  size: 700814
  timestamp: 1620479612257
- kind: conda
  name: font-ttf-ubuntu
  version: '0.83'
  build: h77eed37_2
  build_number: 2
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_2.conda
  sha256: c940f6e969143e13a3a9660abb3c7e7e23b8319efb29dbdd5dee0b9939236e13
  md5: cbbe59391138ea5ad3658c76912e147f
  license: LicenseRef-Ubuntu-Font-Licence-Version-1.0
  license_family: Other
  size: 1622566
  timestamp: 1714483134319
- kind: conda
  name: fontconfig
  version: 2.14.2
  build: h14ed4e7_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.14.2-h14ed4e7_0.conda
  sha256: 155d534c9037347ea7439a2c6da7c24ffec8e5dd278889b4c57274a1d91e0a83
  md5: 0f69b688f52ff6da70bccb7ff7001d1d
  depends:
  - expat >=2.5.0,<3.0a0
  - freetype >=2.12.1,<3.0a0
  - libgcc-ng >=12
  - libuuid >=2.32.1,<3.0a0
  - libzlib >=1.2.13,<1.3.0a0
  license: MIT
  license_family: MIT
  size: 272010
  timestamp: 1674828850194
- kind: conda
  name: fonts-conda-ecosystem
  version: '1'
  build: '0'
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
  sha256: a997f2f1921bb9c9d76e6fa2f6b408b7fa549edd349a77639c9fe7a23ea93e61
  md5: fee5683a3f04bd15cbd8318b096a27ab
  depends:
  - fonts-conda-forge
  license: BSD-3-Clause
  license_family: BSD
  size: 3667
  timestamp: 1566974674465
- kind: conda
  name: fonts-conda-forge
  version: '1'
  build: '0'
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
  sha256: 53f23a3319466053818540bcdf2091f253cbdbab1e0e9ae7b9e509dcaa2a5e38
  md5: f766549260d6815b0c52253f1fb1bb29
  depends:
  - font-ttf-dejavu-sans-mono
  - font-ttf-inconsolata
  - font-ttf-source-code-pro
  - font-ttf-ubuntu
  license: BSD-3-Clause
  license_family: BSD
  size: 4102
  timestamp: 1566932280397
- kind: pypi
  name: fonttools
  version: 4.51.0
  url: https://files.pythonhosted.org/packages/67/09/e09ee013d9d6f2f006147e5fc2b4d807eb2931f4f890c2d4f711e10391d7/fonttools-4.51.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: 180194c7fe60c989bb627d7ed5011f2bef1c4d36ecf3ec64daec8302f1ae0716
  requires_dist:
  - fs<3,>=2.2.0 ; extra == 'all'
  - lxml>=4.0 ; extra == 'all'
  - zopfli>=0.1.4 ; extra == 'all'
  - lz4>=******* ; extra == 'all'
  - pycairo ; extra == 'all'
  - matplotlib ; extra == 'all'
  - sympy ; extra == 'all'
  - skia-pathops>=0.5.0 ; extra == 'all'
  - uharfbuzz>=0.23.0 ; extra == 'all'
  - brotlicffi>=0.8.0 ; platform_python_implementation != 'CPython' and extra == 'all'
  - scipy ; platform_python_implementation != 'PyPy' and extra == 'all'
  - brotli>=1.0.1 ; platform_python_implementation == 'CPython' and extra == 'all'
  - munkres ; platform_python_implementation == 'PyPy' and extra == 'all'
  - unicodedata2>=15.1.0 ; python_version <= '3.12' and extra == 'all'
  - xattr ; sys_platform == 'darwin' and extra == 'all'
  - lz4>=******* ; extra == 'graphite'
  - pycairo ; extra == 'interpolatable'
  - scipy ; platform_python_implementation != 'PyPy' and extra == 'interpolatable'
  - munkres ; platform_python_implementation == 'PyPy' and extra == 'interpolatable'
  - lxml>=4.0 ; extra == 'lxml'
  - skia-pathops>=0.5.0 ; extra == 'pathops'
  - matplotlib ; extra == 'plot'
  - uharfbuzz>=0.23.0 ; extra == 'repacker'
  - sympy ; extra == 'symfont'
  - xattr ; sys_platform == 'darwin' and extra == 'type1'
  - fs<3,>=2.2.0 ; extra == 'ufo'
  - unicodedata2>=15.1.0 ; python_version <= '3.12' and extra == 'unicode'
  - zopfli>=0.1.4 ; extra == 'woff'
  - brotlicffi>=0.8.0 ; platform_python_implementation != 'CPython' and extra == 'woff'
  - brotli>=1.0.1 ; platform_python_implementation == 'CPython' and extra == 'woff'
  requires_python: '>=3.8'
- kind: pypi
  name: fpsample
  version: 0.3.3
  url: https://files.pythonhosted.org/packages/d5/40/80f36394f336a2f577d3acd1e3edad35fa7d9542afdca0635b38910f38cd/fpsample-0.3.3-cp310-cp310-manylinux_2_28_x86_64.whl
  sha256: 3721f6ca25c1327b3367c9a96107e6a38555e61fa0b4981b1ac89a56ef4d56f7
  requires_dist:
  - numpy>=1.16.0
  requires_python: '>=3.7'
- kind: pypi
  name: fqdn
  version: 1.5.1
  url: https://files.pythonhosted.org/packages/cf/58/8acf1b3e91c58313ce5cb67df61001fc9dcd21be4fadb76c1a2d540e09ed/fqdn-1.5.1-py3-none-any.whl
  sha256: 3a179af3761e4df6eb2e026ff9e1a3033d3587bf980a0b1b2e1e5d08d7358014
  requires_dist:
  - cached-property>=1.3.0 ; python_version < '3.8'
  requires_python: '>=2.7,!=3.0,!=3.1,!=3.2,!=3.3,!=3.4,<4'
- kind: conda
  name: freeimage
  version: 3.18.0
  build: h4b96d29_20
  build_number: 20
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/freeimage-3.18.0-h4b96d29_20.conda
  sha256: 07d34a47867f15878dff3d5ae11a7fa24bb03587878ce1798314d03fc6d3d6a5
  md5: 41069afbb9fb02e6e19dd80b4a2c46e7
  depends:
  - imath >=3.1.11,<3.1.12.0a0
  - jxrlib >=1.1,<1.2.0a0
  - libgcc-ng >=12
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libpng >=1.6.43,<1.7.0a0
  - libraw >=0.21.1,<0.22.0a0
  - libstdcxx-ng >=12
  - libtiff >=4.6.0,<4.7.0a0
  - libwebp-base >=1.3.2,<2.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - openexr >=3.2.2,<3.3.0a0
  - openjpeg >=2.5.2,<3.0a0
  license: GPL-2.0-or-later OR GPL-3.0-or-later OR FreeImage
  size: 461394
  timestamp: 1709288677517
- kind: conda
  name: freetype
  version: 2.12.1
  build: h267a509_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.12.1-h267a509_2.conda
  sha256: b2e3c449ec9d907dd4656cb0dc93e140f447175b125a3824b31368b06c666bb6
  md5: 9ae35c3d96db2c94ce0cef86efdfa2cb
  depends:
  - libgcc-ng >=12
  - libpng >=1.6.39,<1.7.0a0
  - libzlib >=1.2.13,<1.3.0a0
  license: GPL-2.0-only OR FTL
  size: 634972
  timestamp: 1694615932610
- kind: conda
  name: fribidi
  version: 1.0.10
  build: h36c2ea0_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/fribidi-1.0.10-h36c2ea0_0.tar.bz2
  sha256: 5d7b6c0ee7743ba41399e9e05a58ccc1cfc903942e49ff6f677f6e423ea7a627
  md5: ac7bc6a654f8f41b352b38f4051135f8
  depends:
  - libgcc-ng >=7.5.0
  license: LGPL-2.1
  size: 114383
  timestamp: 1604416621168
- kind: pypi
  name: gdown
  version: 5.1.0
  url: https://files.pythonhosted.org/packages/cb/56/f4845ed78723a4eb8eb22bcfcb46e1157a462c78c0a5ed318c68c98f9a79/gdown-5.1.0-py3-none-any.whl
  sha256: 421530fd238fa15d41ba43219a79fdc28efe8ac11022173abad333701b77de2c
  requires_dist:
  - beautifulsoup4
  - filelock
  - requests[socks]
  - tqdm
  - build ; extra == 'test'
  - mypy ; extra == 'test'
  - pytest ; extra == 'test'
  - pytest-xdist ; extra == 'test'
  - ruff ; extra == 'test'
  - twine ; extra == 'test'
  - types-requests ; extra == 'test'
  requires_python: '>=3.8'
- kind: conda
  name: gds-tools
  version: ********
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/gds-tools-********-0.tar.bz2
  md5: 94165b72994808d935d15cc3667100c9
  depends:
  - libcufile >=********
  arch: x86_64
  platform: linux
  size: 1680
  timestamp: 1655921106799
- kind: conda
  name: gettext
  version: 0.22.5
  build: h59595ed_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/gettext-0.22.5-h59595ed_2.conda
  sha256: 386181254ddd2aed1fccdfc217da5b6545f6df4e9979ad8e08f5e91e22eaf7dc
  md5: 219ba82e95d7614cf7140d2a4afc0926
  depends:
  - gettext-tools 0.22.5 h59595ed_2
  - libasprintf 0.22.5 h661eb56_2
  - libasprintf-devel 0.22.5 h661eb56_2
  - libgcc-ng >=12
  - libgettextpo 0.22.5 h59595ed_2
  - libgettextpo-devel 0.22.5 h59595ed_2
  - libstdcxx-ng >=12
  license: LGPL-2.1-or-later AND GPL-3.0-or-later
  size: 475058
  timestamp: 1712512357949
- kind: conda
  name: gettext-tools
  version: 0.22.5
  build: h59595ed_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/gettext-tools-0.22.5-h59595ed_2.conda
  sha256: 67d7b1d6fe4f1c516df2000640ec7dcfebf3ff6ea0785f0276870e730c403d33
  md5: 985f2f453fb72408d6b6f1be0f324033
  depends:
  - libgcc-ng >=12
  license: GPL-3.0-or-later
  license_family: GPL
  size: 2728420
  timestamp: 1712512328692
- kind: conda
  name: gflags
  version: 2.2.2
  build: he1b5a44_1004
  build_number: 1004
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/gflags-2.2.2-he1b5a44_1004.tar.bz2
  sha256: a853c0cacf53cfc59e1bca8d6e5cdfe9f38fce836f08c2a69e35429c2a492e77
  md5: cddaf2c63ea4a5901cf09524c490ecdc
  depends:
  - libgcc-ng >=7.5.0
  - libstdcxx-ng >=7.5.0
  license: BSD-3-Clause
  license_family: BSD
  size: 116549
  timestamp: 1594303828933
- kind: pypi
  name: gitdb
  version: 4.0.11
  url: https://files.pythonhosted.org/packages/fd/5b/8f0c4a5bb9fd491c277c21eff7ccae71b47d43c4446c9d0c6cff2fe8c2c4/gitdb-4.0.11-py3-none-any.whl
  sha256: 81a3407ddd2ee8df444cbacea00e2d038e40150acfa3001696fe0dcf1d3adfa4
  requires_dist:
  - smmap<6,>=3.0.1
  requires_python: '>=3.7'
- kind: pypi
  name: gitpython
  version: 3.1.43
  url: https://files.pythonhosted.org/packages/e9/bd/cc3a402a6439c15c3d4294333e13042b915bbeab54edc457c723931fed3f/GitPython-3.1.43-py3-none-any.whl
  sha256: eec7ec56b92aad751f9912a73404bc02ba212a23adb2c7098ee668417051a1ff
  requires_dist:
  - gitdb<5,>=4.0.1
  - typing-extensions>=******* ; python_version < '3.8'
  - sphinx==4.3.2 ; extra == 'doc'
  - sphinx-rtd-theme ; extra == 'doc'
  - sphinxcontrib-applehelp<=1.0.4,>=1.0.2 ; extra == 'doc'
  - sphinxcontrib-devhelp==1.0.2 ; extra == 'doc'
  - sphinxcontrib-htmlhelp<=2.0.1,>=2.0.0 ; extra == 'doc'
  - sphinxcontrib-qthelp==1.0.3 ; extra == 'doc'
  - sphinxcontrib-serializinghtml==1.1.5 ; extra == 'doc'
  - sphinx-autodoc-typehints ; extra == 'doc'
  - coverage[toml] ; extra == 'test'
  - ddt!=1.4.3,>=1.1.1 ; extra == 'test'
  - mypy ; extra == 'test'
  - pre-commit ; extra == 'test'
  - pytest>=7.3.1 ; extra == 'test'
  - pytest-cov ; extra == 'test'
  - pytest-instafail ; extra == 'test'
  - pytest-mock ; extra == 'test'
  - pytest-sugar ; extra == 'test'
  - typing-extensions ; python_version < '3.11' and extra == 'test'
  - mock ; python_version < '3.8' and extra == 'test'
  requires_python: '>=3.7'
- kind: conda
  name: glew
  version: 2.1.0
  build: h9c3ff4c_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/glew-2.1.0-h9c3ff4c_2.tar.bz2
  sha256: 86f5484e38f4604f7694b14f64238e932e8fd8d7364e86557f4911eded2843ae
  md5: fb05eb5c47590b247658243d27fc32f1
  depends:
  - libgcc-ng >=9.3.0
  - libglu
  - libstdcxx-ng >=9.3.0
  - xorg-libx11
  - xorg-libxext
  license: BSD-3-Clause
  license_family: BSD
  size: 662569
  timestamp: 1607113198887
- kind: conda
  name: glib
  version: 2.80.0
  build: hf2295e7_6
  build_number: 6
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/glib-2.80.0-hf2295e7_6.conda
  sha256: 186e366c3a48c07830aa94dfc84616155bdfd08e9b73cb8e482c6ca84a550d3e
  md5: a1e026a82a562b443845db5614ca568a
  depends:
  - glib-tools 2.80.0 hde27a5a_6
  - libffi >=3.4,<4.0a0
  - libgcc-ng >=12
  - libglib 2.80.0 hf2295e7_6
  - python *
  license: LGPL-2.1-or-later
  size: 597788
  timestamp: 1713639483074
- kind: conda
  name: glib-tools
  version: 2.80.0
  build: hde27a5a_6
  build_number: 6
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/glib-tools-2.80.0-hde27a5a_6.conda
  sha256: fb63c92ba2b08aad574404c6229d45f12dc78309ff7a540f1e8d941a8a075074
  md5: a9d23c02485c5cf055f9ac90eb9c9c63
  depends:
  - libgcc-ng >=12
  - libglib 2.80.0 hf2295e7_6
  license: LGPL-2.1-or-later
  size: 113049
  timestamp: 1713639447140
- kind: conda
  name: glog
  version: 0.7.0
  build: hed5481d_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/glog-0.7.0-hed5481d_0.conda
  sha256: 19f41db8f189ed9caec68ffb9ec97d5518b5ee6b58e0636d185f392f688a84a1
  md5: a9ea19c48e11754899299f8123070f4e
  depends:
  - gflags >=2.2.2,<2.3.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 143596
  timestamp: 1708260910243
- kind: conda
  name: gmp
  version: 6.3.0
  build: h59595ed_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-h59595ed_1.conda
  sha256: cfc4202c23d6895d9c84042d08d5cda47d597772df870d4d2a10fc86dded5576
  md5: e358c7c5f6824c272b5034b3816438a7
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: GPL-2.0-or-later OR LGPL-3.0-or-later
  size: 569852
  timestamp: 1710169507479
- kind: conda
  name: gmpy2
  version: 2.1.5
  build: py310hc3586ac_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/gmpy2-2.1.5-py310hc3586ac_0.conda
  sha256: 0d78fa92c9f794e0e2dce37f63388f8d45d211e513f7ed52953035b229745ae7
  md5: 4bc44690e556dd18ecf3a66cbad72d2f
  depends:
  - gmp >=6.3.0,<7.0a0
  - libgcc-ng >=12
  - mpc >=1.3.1,<2.0a0
  - mpfr >=4.2.1,<5.0a0
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: LGPL-3.0-or-later
  license_family: LGPL
  purls:
  - pkg:pypi/gmpy2
  size: 204071
  timestamp: 1713794554076
- kind: conda
  name: gnutls
  version: 3.7.9
  build: hb077bed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/gnutls-3.7.9-hb077bed_0.conda
  sha256: 52d824a5d2b8a5566cd469cae6ad6920469b5a15b3e0ddc609dd29151be71be2
  md5: 33eded89024f21659b1975886a4acf70
  depends:
  - libgcc-ng >=12
  - libidn2 >=2,<3.0a0
  - libstdcxx-ng >=12
  - libtasn1 >=4.19.0,<5.0a0
  - nettle >=3.9.1,<3.10.0a0
  - p11-kit >=0.24.1,<0.25.0a0
  license: LGPL-2.1-or-later
  license_family: LGPL
  size: 1974935
  timestamp: 1701111180127
- kind: conda
  name: graphite2
  version: 1.3.13
  build: h59595ed_1003
  build_number: 1003
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/graphite2-1.3.13-h59595ed_1003.conda
  sha256: 0595b009f20f8f60f13a6398e7cdcbd2acea5f986633adcf85f5a2283c992add
  md5: f87c7b7c2cb45f323ffbce941c78ab7c
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: LGPL-2.0-or-later
  license_family: LGPL
  size: 96855
  timestamp: 1711634169756
- kind: pypi
  name: grpcio
  version: 1.63.0
  url: https://files.pythonhosted.org/packages/47/82/5f51b0ac0e670aa6551f351c6c8a479149a36c413dd76db4b98d26dddbea/grpcio-1.63.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: cf8dae9cc0412cb86c8de5a8f3be395c5119a370f3ce2e69c8b7d46bb9872c8d
  requires_dist:
  - grpcio-tools>=1.63.0 ; extra == 'protobuf'
  requires_python: '>=3.8'
- kind: pypi
  name: gsplat
  version: 1.4.0
  url: https://files.pythonhosted.org/packages/23/69/2e0eff91272e7b03edac615e00d63fba3b20e831452cbf63bd77cbab9c7a/gsplat-1.4.0-py3-none-any.whl
  sha256: 283d96724a8e41af4b338a2882285845ef2e04f3abcdb158bfbe9d515a64cdf0
  requires_dist:
  - ninja
  - numpy
  - jaxtyping
  - rich>=12
  - torch
  - typing-extensions ; python_version < '3.8'
  - black[jupyter]==22.3.0 ; extra == 'dev'
  - isort==5.10.1 ; extra == 'dev'
  - pylint==2.13.4 ; extra == 'dev'
  - pytest==7.1.2 ; extra == 'dev'
  - pytest-xdist==2.5.0 ; extra == 'dev'
  - typeguard>=2.13.3 ; extra == 'dev'
  - pyyaml==6.0 ; extra == 'dev'
  - build ; extra == 'dev'
  - twine ; extra == 'dev'
  requires_python: '>=3.7'
- kind: conda
  name: gst-plugins-base
  version: 1.24.3
  build: h9ad1361_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/gst-plugins-base-1.24.3-h9ad1361_0.conda
  sha256: bfcd03bde2be5293dfb901639778bfe08bc17c59c4935d43cc981953196d7b82
  md5: 8fb0e954c616bb0f9389efac4b4ed44b
  depends:
  - __glibc >=2.17,<3.0.a0
  - alsa-lib >=1.2.11,<1.3.0a0
  - gstreamer 1.24.3 haf2f30d_0
  - libexpat >=2.6.2,<3.0a0
  - libgcc-ng >=12
  - libglib >=2.80.0,<3.0a0
  - libogg >=1.3.4,<1.4.0a0
  - libopus >=1.3.1,<2.0a0
  - libpng >=1.6.43,<1.7.0a0
  - libstdcxx-ng >=12
  - libvorbis >=1.3.7,<1.4.0a0
  - libxcb >=1.15,<1.16.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - xorg-libx11 >=1.8.9,<2.0a0
  - xorg-libxau >=1.0.11,<2.0a0
  - xorg-libxext >=1.3.4,<2.0a0
  - xorg-libxrender >=0.9.11,<0.10.0a0
  license: LGPL-2.0-or-later
  license_family: LGPL
  size: 2794610
  timestamp: 1714842288833
- kind: conda
  name: gstreamer
  version: 1.24.3
  build: haf2f30d_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/gstreamer-1.24.3-haf2f30d_0.conda
  sha256: 020f78890f16e2352f8e9ac12ada652fa0465761aa61b95100c9331e7a1c5742
  md5: f3df87cc9ef0b5113bff55aefcbcafd5
  depends:
  - __glibc >=2.17,<3.0.a0
  - glib >=2.80.0,<3.0a0
  - libgcc-ng >=12
  - libglib >=2.80.0,<3.0a0
  - libiconv >=1.17,<2.0a0
  - libstdcxx-ng >=12
  license: LGPL-2.0-or-later
  license_family: LGPL
  size: 2024018
  timestamp: 1714842147120
- kind: pypi
  name: h11
  version: 0.14.0
  url: https://files.pythonhosted.org/packages/95/04/ff642e65ad6b90db43e668d70ffb6736436c7ce41fcc549f4e9472234127/h11-0.14.0-py3-none-any.whl
  sha256: e3fe4ac4b851c468cc8363d500db52c2ead036020723024a109d37346efaa761
  requires_dist:
  - typing-extensions ; python_version < '3.8'
  requires_python: '>=3.7'
- kind: pypi
  name: h5py
  version: 3.11.0
  url: https://files.pythonhosted.org/packages/94/00/94bf8573e7487b7c37f2b613fc381880d48ec2311f2e859b8a5817deb4df/h5py-3.11.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: 77b19a40788e3e362b54af4dcf9e6fde59ca016db2c61360aa30b47c7b7cef00
  requires_dist:
  - numpy>=1.17.3
  requires_python: '>=3.8'
- kind: conda
  name: harfbuzz
  version: 8.4.0
  build: h3d44ed6_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/harfbuzz-8.4.0-h3d44ed6_0.conda
  sha256: d27441d53498f28a36a1612d8f767bae0418076e9c08dcd2cd511c8439d2fb4d
  md5: 27f46291a6aaa3c2a4f798ebd35a7ddb
  depends:
  - cairo >=1.18.0,<2.0a0
  - freetype >=2.12.1,<3.0a0
  - graphite2
  - icu >=73.2,<74.0a0
  - libgcc-ng >=12
  - libglib >=2.80.0,<3.0a0
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  size: 1587652
  timestamp: 1713957638950
- kind: conda
  name: hdf5
  version: 1.14.3
  build: nompi_h4f84152_101
  build_number: 101
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/hdf5-1.14.3-nompi_h4f84152_101.conda
  sha256: e7d2591bc77d47e9f3fc57d94a817dc9385f4079d930a93475fe45aa2ba81d47
  md5: 7e98860d08eea82c8057abd78864fcb4
  depends:
  - libaec >=1.1.3,<2.0a0
  - libcurl >=8.7.1,<9.0a0
  - libgcc-ng >=12
  - libgfortran-ng
  - libgfortran5 >=12.3.0
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - openssl >=3.3.0,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 3884115
  timestamp: 1714575562551
- kind: pypi
  name: httpcore
  version: 1.0.5
  url: https://files.pythonhosted.org/packages/78/d4/e5d7e4f2174f8a4d63c8897d79eb8fe2503f7ecc03282fee1fa2719c2704/httpcore-1.0.5-py3-none-any.whl
  sha256: 421f18bac248b25d310f3cacd198d55b8e6125c107797b609ff9b7a6ba7991b5
  requires_dist:
  - certifi
  - h11<0.15,>=0.13
  - anyio<5.0,>=4.0 ; extra == 'asyncio'
  - h2<5,>=3 ; extra == 'http2'
  - socksio==1.* ; extra == 'socks'
  - trio<0.26.0,>=0.22.0 ; extra == 'trio'
  requires_python: '>=3.8'
- kind: pypi
  name: httpx
  version: 0.27.0
  url: https://files.pythonhosted.org/packages/41/7b/ddacf6dcebb42466abd03f368782142baa82e08fc0c1f8eaa05b4bae87d5/httpx-0.27.0-py3-none-any.whl
  sha256: 71d5465162c13681bff01ad59b2cc68dd838ea1f10e51574bac27103f00c91a5
  requires_dist:
  - anyio
  - certifi
  - httpcore==1.*
  - idna
  - sniffio
  - brotli ; platform_python_implementation == 'CPython' and extra == 'brotli'
  - brotlicffi ; platform_python_implementation != 'CPython' and extra == 'brotli'
  - click==8.* ; extra == 'cli'
  - pygments==2.* ; extra == 'cli'
  - rich<14,>=10 ; extra == 'cli'
  - h2<5,>=3 ; extra == 'http2'
  - socksio==1.* ; extra == 'socks'
  requires_python: '>=3.8'
- kind: conda
  name: icu
  version: '73.2'
  build: h59595ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/icu-73.2-h59595ed_0.conda
  sha256: e12fd90ef6601da2875ebc432452590bc82a893041473bc1c13ef29001a73ea8
  md5: cc47e1facc155f91abd89b11e48e72ff
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  size: 12089150
  timestamp: 1692900650789
- kind: conda
  name: idna
  version: '3.7'
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/idna-3.7-pyhd8ed1ab_0.conda
  sha256: 9687ee909ed46169395d4f99a0ee94b80a52f87bed69cd454bb6d37ffeb0ec7b
  md5: c0cc1420498b17414d8617d0b9f506ca
  depends:
  - python >=3.6
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/idna
  size: 52718
  timestamp: 1713279497047
- kind: pypi
  name: imageio
  version: 2.34.1
  url: https://files.pythonhosted.org/packages/a3/b6/39c7dad203d9984225f47e0aa39ac3ba3a47c77a02d0ef2a7be691855a06/imageio-2.34.1-py3-none-any.whl
  sha256: 408c1d4d62f72c9e8347e7d1ca9bc11d8673328af3913868db3b828e28b40a4c
  requires_dist:
  - numpy
  - pillow>=8.3.2
  - astropy ; extra == 'all-plugins'
  - av ; extra == 'all-plugins'
  - imageio-ffmpeg ; extra == 'all-plugins'
  - pillow-heif ; extra == 'all-plugins'
  - psutil ; extra == 'all-plugins'
  - tifffile ; extra == 'all-plugins'
  - av ; extra == 'all-plugins-pypy'
  - imageio-ffmpeg ; extra == 'all-plugins-pypy'
  - pillow-heif ; extra == 'all-plugins-pypy'
  - psutil ; extra == 'all-plugins-pypy'
  - tifffile ; extra == 'all-plugins-pypy'
  - wheel ; extra == 'build'
  - pytest ; extra == 'dev'
  - pytest-cov ; extra == 'dev'
  - fsspec[github] ; extra == 'dev'
  - black ; extra == 'dev'
  - flake8 ; extra == 'dev'
  - sphinx<6 ; extra == 'docs'
  - numpydoc ; extra == 'docs'
  - pydata-sphinx-theme ; extra == 'docs'
  - imageio-ffmpeg ; extra == 'ffmpeg'
  - psutil ; extra == 'ffmpeg'
  - astropy ; extra == 'fits'
  - astropy ; extra == 'full'
  - av ; extra == 'full'
  - black ; extra == 'full'
  - flake8 ; extra == 'full'
  - fsspec[github] ; extra == 'full'
  - gdal ; extra == 'full'
  - imageio-ffmpeg ; extra == 'full'
  - itk ; extra == 'full'
  - numpydoc ; extra == 'full'
  - pillow-heif ; extra == 'full'
  - psutil ; extra == 'full'
  - pydata-sphinx-theme ; extra == 'full'
  - pytest ; extra == 'full'
  - pytest-cov ; extra == 'full'
  - sphinx<6 ; extra == 'full'
  - tifffile ; extra == 'full'
  - wheel ; extra == 'full'
  - gdal ; extra == 'gdal'
  - itk ; extra == 'itk'
  - black ; extra == 'linting'
  - flake8 ; extra == 'linting'
  - pillow-heif ; extra == 'pillow-heif'
  - av ; extra == 'pyav'
  - pytest ; extra == 'test'
  - pytest-cov ; extra == 'test'
  - fsspec[github] ; extra == 'test'
  - tifffile ; extra == 'tifffile'
  requires_python: '>=3.8'
- kind: conda
  name: imath
  version: 3.1.11
  build: hfc55251_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/imath-3.1.11-hfc55251_0.conda
  sha256: b394465d3c6a9c5b17351562a87df2a5ef541d66f1a2d95d80fe28c9ca7638c3
  md5: 07268e57799c7ad50809cadc297a515e
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 162530
  timestamp: 1709194196768
- kind: pypi
  name: importlib-metadata
  version: 7.1.0
  url: https://files.pythonhosted.org/packages/2d/0a/679461c511447ffaf176567d5c496d1de27cbe34a87df6677d7171b2fbd4/importlib_metadata-7.1.0-py3-none-any.whl
  sha256: 30962b96c0c223483ed6cc7280e7f0199feb01a0e40cfae4d4450fc6fab1f570
  requires_dist:
  - zipp>=0.5
  - typing-extensions>=3.6.4 ; python_version < '3.8'
  - sphinx>=3.5 ; extra == 'docs'
  - jaraco-packaging>=9.3 ; extra == 'docs'
  - rst-linker>=1.9 ; extra == 'docs'
  - furo ; extra == 'docs'
  - sphinx-lint ; extra == 'docs'
  - jaraco-tidelift>=1.4 ; extra == 'docs'
  - ipython ; extra == 'perf'
  - pytest>=6 ; extra == 'testing'
  - pytest-checkdocs>=2.4 ; extra == 'testing'
  - pytest-cov ; extra == 'testing'
  - pytest-enabler>=2.2 ; extra == 'testing'
  - pytest-ruff>=0.2.1 ; extra == 'testing'
  - packaging ; extra == 'testing'
  - pyfakefs ; extra == 'testing'
  - flufl-flake8 ; extra == 'testing'
  - pytest-perf>=0.9.2 ; extra == 'testing'
  - jaraco-test>=5.4 ; extra == 'testing'
  - pytest-mypy ; platform_python_implementation != 'PyPy' and extra == 'testing'
  - importlib-resources>=1.3 ; python_version < '3.9' and extra == 'testing'
  requires_python: '>=3.8'
- kind: pypi
  name: ipykernel
  version: 6.29.4
  url: https://files.pythonhosted.org/packages/53/9d/40d5207db523363d9b5698f33778c18b0d591e3fdb6e0116b894b2a2491c/ipykernel-6.29.4-py3-none-any.whl
  sha256: 1181e653d95c6808039c509ef8e67c4126b3b3af7781496c7cbfb5ed938a27da
  requires_dist:
  - appnope ; platform_system == 'Darwin'
  - comm>=0.1.1
  - debugpy>=1.6.5
  - ipython>=7.23.1
  - jupyter-client>=6.1.12
  - jupyter-core!=5.0.*,>=4.12
  - matplotlib-inline>=0.1
  - nest-asyncio
  - packaging
  - psutil
  - pyzmq>=24
  - tornado>=6.1
  - traitlets>=5.4.0
  - coverage[toml] ; extra == 'cov'
  - curio ; extra == 'cov'
  - matplotlib ; extra == 'cov'
  - pytest-cov ; extra == 'cov'
  - trio ; extra == 'cov'
  - myst-parser ; extra == 'docs'
  - pydata-sphinx-theme ; extra == 'docs'
  - sphinx ; extra == 'docs'
  - sphinx-autodoc-typehints ; extra == 'docs'
  - sphinxcontrib-github-alt ; extra == 'docs'
  - sphinxcontrib-spelling ; extra == 'docs'
  - trio ; extra == 'docs'
  - pyqt5 ; extra == 'pyqt5'
  - pyside6 ; extra == 'pyside6'
  - flaky ; extra == 'test'
  - ipyparallel ; extra == 'test'
  - pre-commit ; extra == 'test'
  - pytest-asyncio>=0.23.5 ; extra == 'test'
  - pytest-cov ; extra == 'test'
  - pytest-timeout ; extra == 'test'
  - pytest>=7.0 ; extra == 'test'
  requires_python: '>=3.8'
- kind: pypi
  name: ipython
  version: 8.24.0
  url: https://files.pythonhosted.org/packages/71/1b/c7bbd3e03ee6f3580a8afbdf8d6fd38279da03bd5c4bc431907ea3246f9a/ipython-8.24.0-py3-none-any.whl
  sha256: d7bf2f6c4314984e3e02393213bab8703cf163ede39672ce5918c51fe253a2a3
  requires_dist:
  - decorator
  - jedi>=0.16
  - matplotlib-inline
  - prompt-toolkit<3.1.0,>=3.0.41
  - pygments>=2.4.0
  - stack-data
  - traitlets>=5.13.0
  - exceptiongroup ; python_version < '3.11'
  - typing-extensions>=4.6 ; python_version < '3.12'
  - pexpect>4.3 ; sys_platform != 'win32' and sys_platform != 'emscripten'
  - colorama ; sys_platform == 'win32'
  - ipython[black,doc,kernel,matplotlib,nbconvert,nbformat,notebook,parallel,qtconsole] ; extra == 'all'
  - ipython[test,test-extra] ; extra == 'all'
  - black ; extra == 'black'
  - ipykernel ; extra == 'doc'
  - setuptools>=18.5 ; extra == 'doc'
  - sphinx>=1.3 ; extra == 'doc'
  - sphinx-rtd-theme ; extra == 'doc'
  - sphinxcontrib-jquery ; extra == 'doc'
  - docrepr ; extra == 'doc'
  - matplotlib ; extra == 'doc'
  - stack-data ; extra == 'doc'
  - typing-extensions ; extra == 'doc'
  - exceptiongroup ; extra == 'doc'
  - ipython[test] ; extra == 'doc'
  - ipykernel ; extra == 'kernel'
  - matplotlib ; extra == 'matplotlib'
  - nbconvert ; extra == 'nbconvert'
  - nbformat ; extra == 'nbformat'
  - ipywidgets ; extra == 'notebook'
  - notebook ; extra == 'notebook'
  - ipyparallel ; extra == 'parallel'
  - qtconsole ; extra == 'qtconsole'
  - pytest ; extra == 'test'
  - pytest-asyncio<0.22 ; extra == 'test'
  - testpath ; extra == 'test'
  - pickleshare ; extra == 'test'
  - ipython[test] ; extra == 'test-extra'
  - curio ; extra == 'test-extra'
  - matplotlib!=3.2.0 ; extra == 'test-extra'
  - nbformat ; extra == 'test-extra'
  - numpy>=1.23 ; extra == 'test-extra'
  - pandas ; extra == 'test-extra'
  - trio ; extra == 'test-extra'
  requires_python: '>=3.10'
- kind: pypi
  name: ipywidgets
  version: 8.1.2
  url: https://files.pythonhosted.org/packages/70/1a/7edeedb1c089d63ccd8bd5c0612334774e90cf9337de9fe6c82d90081791/ipywidgets-8.1.2-py3-none-any.whl
  sha256: bbe43850d79fb5e906b14801d6c01402857996864d1e5b6fa62dd2ee35559f60
  requires_dist:
  - comm>=0.1.3
  - ipython>=6.1.0
  - traitlets>=4.3.1
  - widgetsnbextension~=4.0.10
  - jupyterlab-widgets~=3.0.10
  - jsonschema ; extra == 'test'
  - ipykernel ; extra == 'test'
  - pytest>=3.6.0 ; extra == 'test'
  - pytest-cov ; extra == 'test'
  - pytz ; extra == 'test'
  requires_python: '>=3.7'
- kind: pypi
  name: isoduration
  version: 20.11.0
  url: https://files.pythonhosted.org/packages/7b/55/e5326141505c5d5e34c5e0935d2908a74e4561eca44108fbfb9c13d2911a/isoduration-20.11.0-py3-none-any.whl
  sha256: b2904c2a4228c3d44f409c8ae8e2370eb21a26f7ac2ec5446df141dde3452042
  requires_dist:
  - arrow>=0.15.0
  requires_python: '>=3.7'
- kind: pypi
  name: itsdangerous
  version: 2.2.0
  url: https://files.pythonhosted.org/packages/04/96/92447566d16df59b2a776c0fb82dbc4d9e07cd95062562af01e408583fc4/itsdangerous-2.2.0-py3-none-any.whl
  sha256: c6242fc49e35958c8b15141343aa660db5fc54d4f13a1db01a3f5891b98700ef
  requires_python: '>=3.8'
- kind: pypi
  name: jaxtyping
  version: 0.3.0
  url: https://files.pythonhosted.org/packages/e9/6e/ce242e8f39919e1af817af194189c5ed2cd2a02dae242563932750787e6b/jaxtyping-0.3.0-py3-none-any.whl
  sha256: 4b20d4e7c94d6a2850d78d7849cf33e38a87b993f2f78977d8093efb42cdb892
  requires_dist:
  - wadler-lindig>=0.1.3
  - hippogriffe==0.1.0 ; extra == 'docs'
  - mkdocs-include-exclude-files==0.1.0 ; extra == 'docs'
  - mkdocs-ipynb==0.1.0 ; extra == 'docs'
  - mkdocs-material==9.6.7 ; extra == 'docs'
  - mkdocs==1.6.1 ; extra == 'docs'
  - mkdocstrings[python]==0.28.3 ; extra == 'docs'
  - pymdown-extensions==10.14.3 ; extra == 'docs'
  requires_python: '>=3.10'
- kind: pypi
  name: jedi
  version: 0.19.1
  url: https://files.pythonhosted.org/packages/20/9f/bc63f0f0737ad7a60800bfd472a4836661adae21f9c2535f3957b1e54ceb/jedi-0.19.1-py2.py3-none-any.whl
  sha256: e983c654fe5c02867aef4cdfce5a2fbb4a50adc0af145f70504238f18ef5e7e0
  requires_dist:
  - parso<0.9.0,>=0.8.3
  - jinja2==2.11.3 ; extra == 'docs'
  - markupsafe==1.1.1 ; extra == 'docs'
  - pygments==2.8.1 ; extra == 'docs'
  - alabaster==0.7.12 ; extra == 'docs'
  - babel==2.9.1 ; extra == 'docs'
  - chardet==4.0.0 ; extra == 'docs'
  - commonmark==0.8.1 ; extra == 'docs'
  - docutils==0.17.1 ; extra == 'docs'
  - future==0.18.2 ; extra == 'docs'
  - idna==2.10 ; extra == 'docs'
  - imagesize==1.2.0 ; extra == 'docs'
  - mock==1.0.1 ; extra == 'docs'
  - packaging==20.9 ; extra == 'docs'
  - pyparsing==2.4.7 ; extra == 'docs'
  - pytz==2021.1 ; extra == 'docs'
  - readthedocs-sphinx-ext==2.1.4 ; extra == 'docs'
  - recommonmark==0.5.0 ; extra == 'docs'
  - requests==2.25.1 ; extra == 'docs'
  - six==1.15.0 ; extra == 'docs'
  - snowballstemmer==2.1.0 ; extra == 'docs'
  - sphinx-rtd-theme==0.4.3 ; extra == 'docs'
  - sphinx==1.8.5 ; extra == 'docs'
  - sphinxcontrib-serializinghtml==1.1.4 ; extra == 'docs'
  - sphinxcontrib-websupport==1.2.4 ; extra == 'docs'
  - urllib3==1.26.4 ; extra == 'docs'
  - flake8==5.0.4 ; extra == 'qa'
  - mypy==0.971 ; extra == 'qa'
  - types-setuptools==******** ; extra == 'qa'
  - django ; extra == 'testing'
  - attrs ; extra == 'testing'
  - colorama ; extra == 'testing'
  - docopt ; extra == 'testing'
  - pytest<7.0.0 ; extra == 'testing'
  requires_python: '>=3.6'
- kind: conda
  name: jinja2
  version: 3.1.4
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.4-pyhd8ed1ab_0.conda
  sha256: 27380d870d42d00350d2d52598cddaf02f9505fb24be09488da0c9b8d1428f2d
  md5: 7b86ecb7d3557821c649b3c31e3eb9f2
  depends:
  - markupsafe >=2.0
  - python >=3.7
  license: BSD-3-Clause
  purls:
  - pkg:pypi/jinja2
  size: 111565
  timestamp: 1715127275924
- kind: pypi
  name: joblib
  version: 1.4.2
  url: https://files.pythonhosted.org/packages/91/29/df4b9b42f2be0b623cbd5e2140cafcaa2bef0759a00b7b70104dcfe2fb51/joblib-1.4.2-py3-none-any.whl
  sha256: 06d478d5674cbc267e7496a410ee875abd68e4340feff4490bcb7afb88060ae6
  requires_python: '>=3.8'
- kind: pypi
  name: json5
  version: 0.9.25
  url: https://files.pythonhosted.org/packages/8a/3c/4f8791ee53ab9eeb0b022205aa79387119a74cc9429582ce04098e6fc540/json5-0.9.25-py3-none-any.whl
  sha256: 34ed7d834b1341a86987ed52f3f76cd8ee184394906b6e22a1e0deb9ab294e8f
  requires_python: '>=3.8'
- kind: pypi
  name: jsonpointer
  version: '2.4'
  url: https://files.pythonhosted.org/packages/12/f6/0232cc0c617e195f06f810534d00b74d2f348fe71b2118009ad8ad31f878/jsonpointer-2.4-py2.py3-none-any.whl
  sha256: 15d51bba20eea3165644553647711d150376234112651b4f1811022aecad7d7a
  requires_python: '>=2.7,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*,!=3.5.*,!=3.6.*'
- kind: pypi
  name: jsonschema
  version: 4.22.0
  url: https://files.pythonhosted.org/packages/c8/2f/324fab4be6fe37fb7b521546e8a557e6cf08c1c1b3d0b4839a00f589d9ef/jsonschema-4.22.0-py3-none-any.whl
  sha256: ff4cfd6b1367a40e7bc6411caec72effadd3db0bbe5017de188f2d6108335802
  requires_dist:
  - attrs>=22.2.0
  - importlib-resources>=1.4.0 ; python_version < '3.9'
  - jsonschema-specifications>=2023.3.6
  - pkgutil-resolve-name>=1.3.10 ; python_version < '3.9'
  - referencing>=0.28.4
  - rpds-py>=0.7.1
  - fqdn ; extra == 'format'
  - idna ; extra == 'format'
  - isoduration ; extra == 'format'
  - jsonpointer>1.13 ; extra == 'format'
  - rfc3339-validator ; extra == 'format'
  - rfc3987 ; extra == 'format'
  - uri-template ; extra == 'format'
  - webcolors>=1.11 ; extra == 'format'
  - fqdn ; extra == 'format-nongpl'
  - idna ; extra == 'format-nongpl'
  - isoduration ; extra == 'format-nongpl'
  - jsonpointer>1.13 ; extra == 'format-nongpl'
  - rfc3339-validator ; extra == 'format-nongpl'
  - rfc3986-validator>0.1.0 ; extra == 'format-nongpl'
  - uri-template ; extra == 'format-nongpl'
  - webcolors>=1.11 ; extra == 'format-nongpl'
  requires_python: '>=3.8'
- kind: pypi
  name: jsonschema-specifications
  version: 2023.12.1
  url: https://files.pythonhosted.org/packages/ee/07/44bd408781594c4d0a027666ef27fab1e441b109dc3b76b4f836f8fd04fe/jsonschema_specifications-2023.12.1-py3-none-any.whl
  sha256: 87e4fdf3a94858b8a2ba2778d9ba57d8a9cafca7c7489c46ba0d30a8bc6a9c3c
  requires_dist:
  - importlib-resources>=1.4.0 ; python_version < '3.9'
  - referencing>=0.31.0
  requires_python: '>=3.8'
- kind: pypi
  name: jupyter
  version: 1.0.0
  url: https://files.pythonhosted.org/packages/83/df/0f5dd132200728a86190397e1ea87cd76244e42d39ec5e88efd25b2abd7e/jupyter-1.0.0-py2.py3-none-any.whl
  sha256: 5b290f93b98ffbc21c0c7e749f054b3267782166d72fa5e3ed1ed4eaf34a2b78
  requires_dist:
  - notebook
  - qtconsole
  - jupyter-console
  - nbconvert
  - ipykernel
  - ipywidgets
- kind: pypi
  name: jupyter-client
  version: 8.6.1
  url: https://files.pythonhosted.org/packages/75/6d/d7b55b9c1ac802ab066b3e5015e90faab1fffbbd67a2af498ffc6cc81c97/jupyter_client-8.6.1-py3-none-any.whl
  sha256: 3b7bd22f058434e3b9a7ea4b1500ed47de2713872288c0d511d19926f99b459f
  requires_dist:
  - importlib-metadata>=4.8.3 ; python_version < '3.10'
  - jupyter-core!=5.0.*,>=4.12
  - python-dateutil>=2.8.2
  - pyzmq>=23.0
  - tornado>=6.2
  - traitlets>=5.3
  - ipykernel ; extra == 'docs'
  - myst-parser ; extra == 'docs'
  - pydata-sphinx-theme ; extra == 'docs'
  - sphinx-autodoc-typehints ; extra == 'docs'
  - sphinx>=4 ; extra == 'docs'
  - sphinxcontrib-github-alt ; extra == 'docs'
  - sphinxcontrib-spelling ; extra == 'docs'
  - coverage ; extra == 'test'
  - ipykernel>=6.14 ; extra == 'test'
  - mypy ; extra == 'test'
  - paramiko ; sys_platform == 'win32' and extra == 'test'
  - pre-commit ; extra == 'test'
  - pytest ; extra == 'test'
  - pytest-cov ; extra == 'test'
  - pytest-jupyter[client]>=0.4.1 ; extra == 'test'
  - pytest-timeout ; extra == 'test'
  requires_python: '>=3.8'
- kind: pypi
  name: jupyter-console
  version: 6.6.3
  url: https://files.pythonhosted.org/packages/ca/77/71d78d58f15c22db16328a476426f7ac4a60d3a5a7ba3b9627ee2f7903d4/jupyter_console-6.6.3-py3-none-any.whl
  sha256: 309d33409fcc92ffdad25f0bcdf9a4a9daa61b6f341177570fdac03de5352485
  requires_dist:
  - ipykernel>=6.14
  - ipython
  - jupyter-client>=7.0.0
  - jupyter-core!=5.0.*,>=4.12
  - prompt-toolkit>=3.0.30
  - pygments
  - pyzmq>=17
  - traitlets>=5.4
  - flaky ; extra == 'test'
  - pexpect ; extra == 'test'
  - pytest ; extra == 'test'
  requires_python: '>=3.7'
- kind: pypi
  name: jupyter-core
  version: 5.7.2
  url: https://files.pythonhosted.org/packages/c9/fb/108ecd1fe961941959ad0ee4e12ee7b8b1477247f30b1fdfd83ceaf017f0/jupyter_core-5.7.2-py3-none-any.whl
  sha256: 4f7315d2f6b4bcf2e3e7cb6e46772eba760ae459cd1f59d29eb57b0a01bd7409
  requires_dist:
  - platformdirs>=2.5
  - pywin32>=300 ; sys_platform == 'win32' and platform_python_implementation != 'PyPy'
  - traitlets>=5.3
  - myst-parser ; extra == 'docs'
  - pydata-sphinx-theme ; extra == 'docs'
  - sphinx-autodoc-typehints ; extra == 'docs'
  - sphinxcontrib-github-alt ; extra == 'docs'
  - sphinxcontrib-spelling ; extra == 'docs'
  - traitlets ; extra == 'docs'
  - ipykernel ; extra == 'test'
  - pre-commit ; extra == 'test'
  - pytest-cov ; extra == 'test'
  - pytest-timeout ; extra == 'test'
  - pytest<8 ; extra == 'test'
  requires_python: '>=3.8'
- kind: pypi
  name: jupyter-events
  version: 0.10.0
  url: https://files.pythonhosted.org/packages/a5/94/059180ea70a9a326e1815176b2370da56376da347a796f8c4f0b830208ef/jupyter_events-0.10.0-py3-none-any.whl
  sha256: 4b72130875e59d57716d327ea70d3ebc3af1944d3717e5a498b8a06c6c159960
  requires_dist:
  - jsonschema[format-nongpl]>=4.18.0
  - python-json-logger>=2.0.4
  - pyyaml>=5.3
  - referencing
  - rfc3339-validator
  - rfc3986-validator>=0.1.1
  - traitlets>=5.3
  - click ; extra == 'cli'
  - rich ; extra == 'cli'
  - jupyterlite-sphinx ; extra == 'docs'
  - myst-parser ; extra == 'docs'
  - pydata-sphinx-theme ; extra == 'docs'
  - sphinxcontrib-spelling ; extra == 'docs'
  - click ; extra == 'test'
  - pre-commit ; extra == 'test'
  - pytest-asyncio>=0.19.0 ; extra == 'test'
  - pytest-console-scripts ; extra == 'test'
  - pytest>=7.0 ; extra == 'test'
  - rich ; extra == 'test'
  requires_python: '>=3.8'
- kind: pypi
  name: jupyter-lsp
  version: 2.2.5
  url: https://files.pythonhosted.org/packages/07/e0/7bd7cff65594fd9936e2f9385701e44574fc7d721331ff676ce440b14100/jupyter_lsp-2.2.5-py3-none-any.whl
  sha256: 45fbddbd505f3fbfb0b6cb2f1bc5e15e83ab7c79cd6e89416b248cb3c00c11da
  requires_dist:
  - jupyter-server>=1.1.2
  - importlib-metadata>=4.8.3 ; python_version < '3.10'
  requires_python: '>=3.8'
- kind: pypi
  name: jupyter-server
  version: 2.13.0
  url: https://files.pythonhosted.org/packages/95/85/483b8e09a897d1bc2194646d30d4ce6ae166106e91ecbd11d6b6d9ccfc36/jupyter_server-2.13.0-py3-none-any.whl
  sha256: 77b2b49c3831fbbfbdb5048cef4350d12946191f833a24e5f83e5f8f4803e97b
  requires_dist:
  - anyio>=3.1.0
  - argon2-cffi
  - jinja2
  - jupyter-client>=7.4.4
  - jupyter-core!=5.0.*,>=4.12
  - jupyter-events>=0.9.0
  - jupyter-server-terminals
  - nbconvert>=6.4.4
  - nbformat>=5.3.0
  - overrides
  - packaging
  - prometheus-client
  - pywinpty ; os_name == 'nt'
  - pyzmq>=24
  - send2trash>=1.8.2
  - terminado>=0.8.3
  - tornado>=6.2.0
  - traitlets>=5.6.0
  - websocket-client
  - ipykernel ; extra == 'docs'
  - jinja2 ; extra == 'docs'
  - jupyter-client ; extra == 'docs'
  - jupyter-server ; extra == 'docs'
  - myst-parser ; extra == 'docs'
  - nbformat ; extra == 'docs'
  - prometheus-client ; extra == 'docs'
  - pydata-sphinx-theme ; extra == 'docs'
  - send2trash ; extra == 'docs'
  - sphinx-autodoc-typehints ; extra == 'docs'
  - sphinxcontrib-github-alt ; extra == 'docs'
  - sphinxcontrib-openapi>=0.8.0 ; extra == 'docs'
  - sphinxcontrib-spelling ; extra == 'docs'
  - sphinxemoji ; extra == 'docs'
  - tornado ; extra == 'docs'
  - typing-extensions ; extra == 'docs'
  - flaky ; extra == 'test'
  - ipykernel ; extra == 'test'
  - pre-commit ; extra == 'test'
  - pytest-console-scripts ; extra == 'test'
  - pytest-jupyter[server]>=0.7 ; extra == 'test'
  - pytest-timeout ; extra == 'test'
  - pytest>=7.0 ; extra == 'test'
  - requests ; extra == 'test'
  requires_python: '>=3.8'
- kind: pypi
  name: jupyter-server-terminals
  version: 0.5.3
  url: https://files.pythonhosted.org/packages/07/2d/2b32cdbe8d2a602f697a649798554e4f072115438e92249624e532e8aca6/jupyter_server_terminals-0.5.3-py3-none-any.whl
  sha256: 41ee0d7dc0ebf2809c668e0fc726dfaf258fcd3e769568996ca731b6194ae9aa
  requires_dist:
  - pywinpty>=2.0.3 ; os_name == 'nt'
  - terminado>=0.8.3
  - jinja2 ; extra == 'docs'
  - jupyter-server ; extra == 'docs'
  - mistune<4.0 ; extra == 'docs'
  - myst-parser ; extra == 'docs'
  - nbformat ; extra == 'docs'
  - packaging ; extra == 'docs'
  - pydata-sphinx-theme ; extra == 'docs'
  - sphinxcontrib-github-alt ; extra == 'docs'
  - sphinxcontrib-openapi ; extra == 'docs'
  - sphinxcontrib-spelling ; extra == 'docs'
  - sphinxemoji ; extra == 'docs'
  - tornado ; extra == 'docs'
  - jupyter-server>=2.0.0 ; extra == 'test'
  - pytest-jupyter[server]>=0.5.3 ; extra == 'test'
  - pytest-timeout ; extra == 'test'
  - pytest>=7.0 ; extra == 'test'
  requires_python: '>=3.8'
- kind: pypi
  name: jupyterlab
  version: 4.2.0
  url: https://files.pythonhosted.org/packages/72/c3/532326adbb2b76f709e3e582aeefd0a85bd7454599ff450d90dd9540f5ed/jupyterlab-4.2.0-py3-none-any.whl
  sha256: 0dfe9278e25a145362289c555d9beb505697d269c10e99909766af7c440ad3cc
  requires_dist:
  - async-lru>=1.0.0
  - httpx>=0.25.0
  - importlib-metadata>=4.8.3 ; python_version < '3.10'
  - importlib-resources>=1.4 ; python_version < '3.9'
  - ipykernel>=6.5.0
  - jinja2>=3.0.3
  - jupyter-core
  - jupyter-lsp>=2.0.0
  - jupyter-server<3,>=2.4.0
  - jupyterlab-server<3,>=2.27.1
  - notebook-shim>=0.2
  - packaging
  - tomli>=1.2.2 ; python_version < '3.11'
  - tornado>=6.2.0
  - traitlets
  - build ; extra == 'dev'
  - bump2version ; extra == 'dev'
  - coverage ; extra == 'dev'
  - hatch ; extra == 'dev'
  - pre-commit ; extra == 'dev'
  - pytest-cov ; extra == 'dev'
  - ruff==0.3.5 ; extra == 'dev'
  - jsx-lexer ; extra == 'docs'
  - myst-parser ; extra == 'docs'
  - pydata-sphinx-theme>=0.13.0 ; extra == 'docs'
  - pytest ; extra == 'docs'
  - pytest-check-links ; extra == 'docs'
  - pytest-jupyter ; extra == 'docs'
  - sphinx-copybutton ; extra == 'docs'
  - sphinx<7.3.0,>=1.8 ; extra == 'docs'
  - altair==5.3.0 ; extra == 'docs-screenshots'
  - ipython==8.16.1 ; extra == 'docs-screenshots'
  - ipywidgets==8.1.2 ; extra == 'docs-screenshots'
  - jupyterlab-geojson==3.4.0 ; extra == 'docs-screenshots'
  - jupyterlab-language-pack-zh-cn==4.1.post2 ; extra == 'docs-screenshots'
  - matplotlib==3.8.3 ; extra == 'docs-screenshots'
  - nbconvert>=7.0.0 ; extra == 'docs-screenshots'
  - pandas==2.2.1 ; extra == 'docs-screenshots'
  - scipy==1.12.0 ; extra == 'docs-screenshots'
  - vega-datasets==0.9.0 ; extra == 'docs-screenshots'
  - coverage ; extra == 'test'
  - pytest-check-links>=0.7 ; extra == 'test'
  - pytest-console-scripts ; extra == 'test'
  - pytest-cov ; extra == 'test'
  - pytest-jupyter>=0.5.3 ; extra == 'test'
  - pytest-timeout ; extra == 'test'
  - pytest-tornasync ; extra == 'test'
  - pytest>=7.0 ; extra == 'test'
  - requests ; extra == 'test'
  - requests-cache ; extra == 'test'
  - virtualenv ; extra == 'test'
  - copier<10,>=8 ; extra == 'upgrade-extension'
  - jinja2-time<0.3 ; extra == 'upgrade-extension'
  - pydantic<2.0 ; extra == 'upgrade-extension'
  - pyyaml-include<2.0 ; extra == 'upgrade-extension'
  - tomli-w<2.0 ; extra == 'upgrade-extension'
  requires_python: '>=3.8'
- kind: pypi
  name: jupyterlab-pygments
  version: 0.3.0
  url: https://files.pythonhosted.org/packages/b1/dd/ead9d8ea85bf202d90cc513b533f9c363121c7792674f78e0d8a854b63b4/jupyterlab_pygments-0.3.0-py3-none-any.whl
  sha256: 841a89020971da1d8693f1a99997aefc5dc424bb1b251fd6322462a1b8842780
  requires_python: '>=3.8'
- kind: pypi
  name: jupyterlab-server
  version: 2.27.1
  url: https://files.pythonhosted.org/packages/2f/b9/ed4ecad7cf1863a64920dc4c19b0376628b5d6bd28d2ec1e00cbac4ba2fb/jupyterlab_server-2.27.1-py3-none-any.whl
  sha256: f5e26156e5258b24d532c84e7c74cc212e203bff93eb856f81c24c16daeecc75
  requires_dist:
  - babel>=2.10
  - importlib-metadata>=4.8.3 ; python_version < '3.10'
  - jinja2>=3.0.3
  - json5>=0.9.0
  - jsonschema>=4.18.0
  - jupyter-server<3,>=1.21
  - packaging>=21.3
  - requests>=2.31
  - autodoc-traits ; extra == 'docs'
  - jinja2<3.2.0 ; extra == 'docs'
  - mistune<4 ; extra == 'docs'
  - myst-parser ; extra == 'docs'
  - pydata-sphinx-theme ; extra == 'docs'
  - sphinx ; extra == 'docs'
  - sphinx-copybutton ; extra == 'docs'
  - sphinxcontrib-openapi>0.8 ; extra == 'docs'
  - openapi-core~=0.18.0 ; extra == 'openapi'
  - ruamel-yaml ; extra == 'openapi'
  - hatch ; extra == 'test'
  - ipykernel ; extra == 'test'
  - openapi-core~=0.18.0 ; extra == 'test'
  - openapi-spec-validator<0.8.0,>=0.6.0 ; extra == 'test'
  - pytest-console-scripts ; extra == 'test'
  - pytest-cov ; extra == 'test'
  - pytest-jupyter[server]>=0.6.2 ; extra == 'test'
  - pytest-timeout ; extra == 'test'
  - pytest<8,>=7.0 ; extra == 'test'
  - requests-mock ; extra == 'test'
  - ruamel-yaml ; extra == 'test'
  - sphinxcontrib-spelling ; extra == 'test'
  - strict-rfc3339 ; extra == 'test'
  - werkzeug ; extra == 'test'
  requires_python: '>=3.8'
- kind: pypi
  name: jupyterlab-widgets
  version: 3.0.10
  url: https://files.pythonhosted.org/packages/24/da/db1cb0387a7e4086780aff137987ee924e953d7f91b2a870f994b9b1eeb8/jupyterlab_widgets-3.0.10-py3-none-any.whl
  sha256: dd61f3ae7a5a7f80299e14585ce6cf3d6925a96c9103c978eda293197730cb64
  requires_python: '>=3.7'
- kind: conda
  name: jxrlib
  version: '1.1'
  build: hd590300_3
  build_number: 3
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/jxrlib-1.1-hd590300_3.conda
  sha256: 2057ca87b313bde5b74b93b0e696f8faab69acd4cb0edebb78469f3f388040c0
  md5: 5aeabe88534ea4169d4c49998f293d6c
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 239104
  timestamp: 1703333860145
- kind: conda
  name: keyutils
  version: 1.6.1
  build: h166bdaf_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
  sha256: 150c05a6e538610ca7c43beb3a40d65c90537497a4f6a5f4d15ec0451b6f5ebb
  md5: 30186d27e2c9fa62b45fb1476b7200e3
  depends:
  - libgcc-ng >=10.3.0
  license: LGPL-2.1-or-later
  size: 117831
  timestamp: 1646151697040
- kind: pypi
  name: kiwisolver
  version: 1.4.5
  url: https://files.pythonhosted.org/packages/6f/40/4ab1fdb57fced80ce5903f04ae1aed7c1d5939dda4fd0c0aa526c12fe28a/kiwisolver-1.4.5-cp310-cp310-manylinux_2_12_x86_64.manylinux2010_x86_64.whl
  sha256: ec20916e7b4cbfb1f12380e46486ec4bcbaa91a9c448b97023fde0d5bbf9e4ff
  requires_dist:
  - typing-extensions ; python_version < '3.8'
  requires_python: '>=3.7'
- kind: conda
  name: krb5
  version: 1.21.2
  build: h659d440_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.2-h659d440_0.conda
  sha256: 259bfaae731989b252b7d2228c1330ef91b641c9d68ff87dae02cbae682cb3e4
  md5: cd95826dbd331ed1be26bdf401432844
  depends:
  - keyutils >=1.6.1,<2.0a0
  - libedit >=3.1.20191231,<3.2.0a0
  - libedit >=3.1.20191231,<4.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - openssl >=3.1.2,<4.0a0
  license: MIT
  license_family: MIT
  size: 1371181
  timestamp: 1692097755782
- kind: conda
  name: lame
  version: '3.100'
  build: h166bdaf_1003
  build_number: 1003
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/lame-3.100-h166bdaf_1003.tar.bz2
  sha256: aad2a703b9d7b038c0f745b853c6bb5f122988fe1a7a096e0e606d9cbec4eaab
  md5: a8832b479f93521a9e7b5b743803be51
  depends:
  - libgcc-ng >=12
  license: LGPL-2.0-only
  license_family: LGPL
  size: 508258
  timestamp: 1664996250081
- kind: pypi
  name: lazy-loader
  version: '0.4'
  url: https://files.pythonhosted.org/packages/83/60/d497a310bde3f01cb805196ac61b7ad6dc5dcf8dce66634dc34364b20b4f/lazy_loader-0.4-py3-none-any.whl
  sha256: 342aa8e14d543a154047afb4ba8ef17f5563baad3fc610d7b15b213b0f119efc
  requires_dist:
  - packaging
  - importlib-metadata ; python_version < '3.8'
  - changelist==0.5 ; extra == 'dev'
  - pre-commit==3.7.0 ; extra == 'lint'
  - pytest>=7.4 ; extra == 'test'
  - pytest-cov>=4.1 ; extra == 'test'
  requires_python: '>=3.7'
- kind: conda
  name: lcms2
  version: '2.16'
  build: hb7c19ff_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/lcms2-2.16-hb7c19ff_0.conda
  sha256: 5c878d104b461b7ef922abe6320711c0d01772f4cd55de18b674f88547870041
  md5: 51bb7010fc86f70eee639b4bb7a894f5
  depends:
  - libgcc-ng >=12
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libtiff >=4.6.0,<4.7.0a0
  license: MIT
  license_family: MIT
  size: 245247
  timestamp: 1701647787198
- kind: conda
  name: ld_impl_linux-64
  version: '2.40'
  build: h55db66e_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.40-h55db66e_0.conda
  sha256: ef969eee228cfb71e55146eaecc6af065f468cb0bc0a5239bc053b39db0b5f09
  md5: 10569984e7db886e4f1abc2b47ad79a1
  constrains:
  - binutils_impl_linux-64 2.40
  license: GPL-3.0-only
  license_family: GPL
  size: 713322
  timestamp: 1713651222435
- kind: conda
  name: lerc
  version: 4.0.0
  build: h27087fc_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h27087fc_0.tar.bz2
  sha256: cb55f36dcd898203927133280ae1dc643368af041a48bcf7c026acb7c47b0c12
  md5: 76bbff344f0134279f225174e9064c8f
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: Apache-2.0
  license_family: Apache
  size: 281798
  timestamp: 1657977462600
- kind: conda
  name: libabseil
  version: '20240116.2'
  build: cxx17_h59595ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libabseil-20240116.2-cxx17_h59595ed_0.conda
  sha256: 19b789dc38dff64eee2002675991e63f381eedf5efd5c85f2dac512ed97376d7
  md5: 682bdbe046a68f749769b492f3625c5c
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  constrains:
  - libabseil-static =20240116.2=cxx17*
  - abseil-cpp =20240116.2
  license: Apache-2.0
  license_family: Apache
  size: 1266634
  timestamp: 1714403128134
- kind: conda
  name: libaec
  version: 1.1.3
  build: h59595ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libaec-1.1.3-h59595ed_0.conda
  sha256: 2ef420a655528bca9d269086cf33b7e90d2f54ad941b437fb1ed5eca87cee017
  md5: 5e97e271911b8b2001a8b71860c32faa
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 35446
  timestamp: 1711021212685
- kind: conda
  name: libarrow
  version: 15.0.2
  build: hefa796f_6_cpu
  build_number: 6
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libarrow-15.0.2-hefa796f_6_cpu.conda
  sha256: d97be4671bc6a76c177a8611677f46fbd9f30227c73b2d26bd8276df4385bf21
  md5: 2aa703494b2c0a1356ec581a24653177
  depends:
  - aws-crt-cpp >=0.26.8,<0.26.9.0a0
  - aws-sdk-cpp >=1.11.267,<1.11.268.0a0
  - bzip2 >=1.0.8,<2.0a0
  - gflags >=2.2.2,<2.3.0a0
  - glog >=0.7.0,<0.8.0a0
  - libabseil * cxx17*
  - libabseil >=20240116.2,<20240117.0a0
  - libbrotlidec >=1.1.0,<1.2.0a0
  - libbrotlienc >=1.1.0,<1.2.0a0
  - libgcc-ng >=12
  - libgoogle-cloud >=2.23.0,<2.24.0a0
  - libgoogle-cloud-storage >=2.23.0,<2.24.0a0
  - libre2-11 >=2023.9.1,<2024.0a0
  - libstdcxx-ng >=12
  - libutf8proc >=2.8.0,<3.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - lz4-c >=1.9.3,<1.10.0a0
  - orc >=2.0.0,<2.0.1.0a0
  - re2
  - snappy >=1.2.0,<1.3.0a0
  - zstd >=1.5.5,<1.6.0a0
  constrains:
  - arrow-cpp <0.0a0
  - apache-arrow-proc =*=cpu
  - parquet-cpp <0.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 8174304
  timestamp: 1714448169194
- kind: conda
  name: libarrow-acero
  version: 15.0.2
  build: hbabe93e_6_cpu
  build_number: 6
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libarrow-acero-15.0.2-hbabe93e_6_cpu.conda
  sha256: 75e88940751abc5b48fc44721e36fcddd7dd5c5bc1d8622ed36fc1c93b26beb1
  md5: 061797e461211bbdc174fdabeb45ac5c
  depends:
  - gflags >=2.2.2,<2.3.0a0
  - libarrow 15.0.2 hefa796f_6_cpu
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: Apache-2.0
  license_family: APACHE
  size: 600779
  timestamp: 1714448211788
- kind: conda
  name: libarrow-dataset
  version: 15.0.2
  build: hbabe93e_6_cpu
  build_number: 6
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libarrow-dataset-15.0.2-hbabe93e_6_cpu.conda
  sha256: 9611a2a415523d5b5533036a73af72deb621f68e1a6cd3bcd0572aceed2a9b4e
  md5: f30638c82fb4ce663122a3714ef0aa7d
  depends:
  - gflags >=2.2.2,<2.3.0a0
  - libarrow 15.0.2 hefa796f_6_cpu
  - libarrow-acero 15.0.2 hbabe93e_6_cpu
  - libgcc-ng >=12
  - libparquet 15.0.2 hacf5a1f_6_cpu
  - libstdcxx-ng >=12
  license: Apache-2.0
  license_family: APACHE
  size: 587827
  timestamp: 1714448297550
- kind: conda
  name: libarrow-flight
  version: 15.0.2
  build: hc4f8a93_6_cpu
  build_number: 6
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libarrow-flight-15.0.2-hc4f8a93_6_cpu.conda
  sha256: b75bfae411b8de65f3ccddf8acec2811d79b21b8da00788b4c8d279616d488e9
  md5: 5fb64b8a0a7b68e48a08cc138d3650b6
  depends:
  - gflags >=2.2.2,<2.3.0a0
  - libabseil * cxx17*
  - libabseil >=20240116.2,<20240117.0a0
  - libarrow 15.0.2 hefa796f_6_cpu
  - libgcc-ng >=12
  - libgrpc >=1.62.2,<1.63.0a0
  - libprotobuf >=4.25.3,<4.25.4.0a0
  - libstdcxx-ng >=12
  - ucx >=1.15.0,<1.16.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 508666
  timestamp: 1714448232421
- kind: conda
  name: libarrow-flight-sql
  version: 15.0.2
  build: he4f5ca8_6_cpu
  build_number: 6
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libarrow-flight-sql-15.0.2-he4f5ca8_6_cpu.conda
  sha256: 761fceb170e0efee8f8c365f17ec706fab12a8e9378d3e80b36ed06b9390cb9b
  md5: 2cd07c4ead6fb6bd1a2741c2a1cb5666
  depends:
  - gflags >=2.2.2,<2.3.0a0
  - libarrow 15.0.2 hefa796f_6_cpu
  - libarrow-flight 15.0.2 hc4f8a93_6_cpu
  - libgcc-ng >=12
  - libprotobuf >=4.25.3,<4.25.4.0a0
  - libstdcxx-ng >=12
  license: Apache-2.0
  license_family: APACHE
  size: 197489
  timestamp: 1714448319450
- kind: conda
  name: libarrow-gandiva
  version: 15.0.2
  build: hc1954e9_6_cpu
  build_number: 6
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libarrow-gandiva-15.0.2-hc1954e9_6_cpu.conda
  sha256: eb9ac1a92a60ddc23e34d007d6d73c095f0edd82f83fd63f7a29436e6c8c74f7
  md5: 80201ecc7f27bd6a2b50b8a7ee3f315f
  depends:
  - gflags >=2.2.2,<2.3.0a0
  - libarrow 15.0.2 hefa796f_6_cpu
  - libgcc-ng >=12
  - libllvm16 >=16.0.6,<16.1.0a0
  - libre2-11 >=2023.9.1,<2024.0a0
  - libstdcxx-ng >=12
  - libutf8proc >=2.8.0,<3.0a0
  - openssl >=3.2.1,<4.0a0
  - re2
  license: Apache-2.0
  license_family: APACHE
  size: 897964
  timestamp: **********
- kind: conda
  name: libarrow-substrait
  version: 15.0.2
  build: he4f5ca8_6_cpu
  build_number: 6
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libarrow-substrait-15.0.2-he4f5ca8_6_cpu.conda
  sha256: 7ec84b1a2ed461184bf3c53304e61468765a2b2151d9522f3f9fac0612808249
  md5: cf594f6982de20afedf7aeb6602ceb89
  depends:
  - gflags >=2.2.2,<2.3.0a0
  - libarrow 15.0.2 hefa796f_6_cpu
  - libarrow-acero 15.0.2 hbabe93e_6_cpu
  - libarrow-dataset 15.0.2 hbabe93e_6_cpu
  - libgcc-ng >=12
  - libprotobuf >=4.25.3,<4.25.4.0a0
  - libstdcxx-ng >=12
  license: Apache-2.0
  license_family: APACHE
  size: 521452
  timestamp: 1714448338650
- kind: conda
  name: libasprintf
  version: 0.22.5
  build: h661eb56_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-0.22.5-h661eb56_2.conda
  sha256: 31d58af7eb54e2938123200239277f14893c5fa4b5d0280c8cf55ae10000638b
  md5: dd197c968bf9760bba0031888d431ede
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: LGPL-2.1-or-later
  size: 43226
  timestamp: 1712512265295
- kind: conda
  name: libasprintf-devel
  version: 0.22.5
  build: h661eb56_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-devel-0.22.5-h661eb56_2.conda
  sha256: 99d26d272a8203d30b3efbe734a99c823499884d7759b4291674438137c4b5ca
  md5: 02e41ab5834dcdcc8590cf29d9526f50
  depends:
  - libasprintf 0.22.5 h661eb56_2
  - libgcc-ng >=12
  license: LGPL-2.1-or-later
  size: 34225
  timestamp: 1712512295117
- kind: conda
  name: libass
  version: 0.17.1
  build: h8fe9dca_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libass-0.17.1-h8fe9dca_1.conda
  sha256: 1bc3e44239a11613627488b7a9b6c021ec6b52c5925abd666832db0cb2a59f05
  md5: c306fd9cc90c0585171167d09135a827
  depends:
  - fontconfig >=2.14.2,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - fribidi >=1.0.10,<2.0a0
  - harfbuzz >=8.1.1,<9.0a0
  - libexpat >=2.5.0,<3.0a0
  - libgcc-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  license: ISC
  license_family: OTHER
  size: 126896
  timestamp: 1693027051367
- kind: conda
  name: libblas
  version: 3.9.0
  build: 1_h86c2bf4_netlib
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-1_h86c2bf4_netlib.tar.bz2
  sha256: c95bf82fbf258350d5f61c653a1a14d2e1cca7302bb348cc8347caa84e7ac9b1
  md5: 91273b91f1023d5c72ffc79e02e7812e
  depends:
  - libgcc-ng >=9.3.0
  - libgfortran-ng
  - libgfortran5 >=9.3.0
  track_features:
  - blas_netlib
  license: BSD-3-Clause
  license_family: BSD
  size: 203305
  timestamp: 1603052017387
- kind: conda
  name: libboost
  version: 1.84.0
  build: h8013b2b_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libboost-1.84.0-h8013b2b_2.conda
  sha256: f38191766b87bbf4099851d40ecc17c5d6cf4f898b76a8d8648a5caaf04bf2da
  md5: 2de948e2fa2aea5f2b23cd02edcba24a
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - icu >=73.2,<74.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - xz >=5.2.6,<6.0a0
  - zstd >=1.5.5,<1.6.0a0
  constrains:
  - boost-cpp =1.84.0
  license: BSL-1.0
  size: 2790076
  timestamp: 1711404148144
- kind: conda
  name: libbrotlicommon
  version: 1.1.0
  build: hd590300_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libbrotlicommon-1.1.0-hd590300_1.conda
  sha256: 40f29d1fab92c847b083739af86ad2f36d8154008cf99b64194e4705a1725d78
  md5: aec6c91c7371c26392a06708a73c70e5
  depends:
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 69403
  timestamp: 1695990007212
- kind: conda
  name: libbrotlidec
  version: 1.1.0
  build: hd590300_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libbrotlidec-1.1.0-hd590300_1.conda
  sha256: 86fc861246fbe5ad85c1b6b3882aaffc89590a48b42d794d3d5c8e6d99e5f926
  md5: f07002e225d7a60a694d42a7bf5ff53f
  depends:
  - libbrotlicommon 1.1.0 hd590300_1
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 32775
  timestamp: 1695990022788
- kind: conda
  name: libbrotlienc
  version: 1.1.0
  build: hd590300_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libbrotlienc-1.1.0-hd590300_1.conda
  sha256: f751b8b1c4754a2a8dfdc3b4040fa7818f35bbf6b10e905a47d3a194b746b071
  md5: 5fc11c6020d421960607d821310fcd4d
  depends:
  - libbrotlicommon 1.1.0 hd590300_1
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 282523
  timestamp: 1695990038302
- kind: conda
  name: libcap
  version: '2.69'
  build: h0f662aa_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libcap-2.69-h0f662aa_0.conda
  sha256: 942f9564b4228609f017b6617425d29a74c43b8a030e12239fa4458e5cb6323c
  md5: 25cb5999faa414e5ccb2c1388f62d3d5
  depends:
  - attr >=2.5.1,<2.6.0a0
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 100582
  timestamp: 1684162447012
- kind: conda
  name: libcblas
  version: 3.9.0
  build: 5_h92ddd45_netlib
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-5_h92ddd45_netlib.tar.bz2
  sha256: 93086c3586c5b1f961cb468995dc35d9b99ca8f10d0b76d594a554029f60670c
  md5: 6a3f536ec30f6e6948211a07b1d04ced
  depends:
  - libblas 3.9.0.*
  - libgcc-ng >=9.3.0
  - libgfortran-ng
  - libgfortran5 >=9.3.0
  track_features:
  - blas_netlib
  license: BSD-3-Clause
  license_family: BSD
  size: 55516
  timestamp: 1618011709500
- kind: conda
  name: libclang-cpp15
  version: 15.0.7
  build: default_h127d8a8_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libclang-cpp15-15.0.7-default_h127d8a8_5.conda
  sha256: 9b0238e705a33da74ca82efd03974f499550f7dada1340cc9cb7c35a92411ed8
  md5: d0a9633b53cdc319b8a1a532ae7822b8
  depends:
  - libgcc-ng >=12
  - libllvm15 >=15.0.7,<15.1.0a0
  - libstdcxx-ng >=12
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  size: 17206402
  timestamp: 1711063711931
- kind: conda
  name: libclang13
  version: 18.1.5
  build: default_h5d6823c_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libclang13-18.1.5-default_h5d6823c_0.conda
  sha256: 60c7cdd313566033910bce884b879f39468eb966b2ac61ea828fe432b8a084c5
  md5: 60c39a00b694c98da03f67a3ba1d7499
  depends:
  - libgcc-ng >=12
  - libllvm18 >=18.1.5,<18.2.0a0
  - libstdcxx-ng >=12
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  size: 11052898
  timestamp: 1714870310416
- kind: conda
  name: libcrc32c
  version: 1.1.2
  build: h9c3ff4c_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libcrc32c-1.1.2-h9c3ff4c_0.tar.bz2
  sha256: fd1d153962764433fe6233f34a72cdeed5dcf8a883a85769e8295ce940b5b0c5
  md5: c965a5aa0d5c1c37ffc62dff36e28400
  depends:
  - libgcc-ng >=9.4.0
  - libstdcxx-ng >=9.4.0
  license: BSD-3-Clause
  license_family: BSD
  size: 20440
  timestamp: 1633683576494
- kind: conda
  name: libcublas
  version: *********
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcublas-*********-0.tar.bz2
  md5: 7700a48c99151d2b77e7838aa0852da9
  arch: x86_64
  platform: linux
  size: 381637889
  timestamp: 1662499145253
- kind: conda
  name: libcublas-dev
  version: *********
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcublas-dev-*********-0.tar.bz2
  md5: d2097d064d1437dbbcce39c3e8018a4e
  depends:
  - libcublas >=*********
  arch: x86_64
  platform: linux
  size: 413253930
  timestamp: 1662499317965
- kind: conda
  name: libcufft
  version: *********
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcufft-*********-0.tar.bz2
  md5: dbb21687334ce5f8e6a233cb18ee406b
  arch: x86_64
  platform: linux
  size: 149741913
  timestamp: 1661545628099
- kind: conda
  name: libcufft-dev
  version: *********
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcufft-dev-*********-0.tar.bz2
  md5: bd0ef1722efed6b4d689043a1bbd72fc
  depends:
  - libcufft >=*********
  arch: x86_64
  platform: linux
  size: 289240757
  timestamp: 1661545712353
- kind: conda
  name: libcufile
  version: ********
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcufile-********-0.tar.bz2
  md5: eacba45944720ae98bea25f468a11ca7
  arch: x86_64
  platform: linux
  size: 561413
  timestamp: 1655921105488
- kind: conda
  name: libcufile-dev
  version: ********
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcufile-dev-********-0.tar.bz2
  md5: a8dfd661b50be828ea8e58d6fdd01363
  depends:
  - libcufile >=********
  arch: x86_64
  platform: linux
  size: 1671287
  timestamp: 1655921107481
- kind: conda
  name: libcups
  version: 2.3.3
  build: h4637d8d_4
  build_number: 4
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libcups-2.3.3-h4637d8d_4.conda
  sha256: bc67b9b21078c99c6bd8595fe7e1ed6da1f721007726e717f0449de7032798c4
  md5: d4529f4dff3057982a7617c7ac58fde3
  depends:
  - krb5 >=1.21.1,<1.22.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  license: Apache-2.0
  license_family: Apache
  size: 4519402
  timestamp: 1689195353551
- kind: conda
  name: libcurand
  version: 10.3.0.86
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcurand-10.3.0.86-0.tar.bz2
  md5: e0358f66f787e409b29becd898d5983f
  arch: x86_64
  platform: linux
  size: 55769596
  timestamp: 1661544586107
- kind: conda
  name: libcurand-dev
  version: 10.3.0.86
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcurand-dev-10.3.0.86-0.tar.bz2
  md5: 5639b483c7c51ce2acb578444c0e10ce
  depends:
  - libcurand >=10.3.0.86
  arch: x86_64
  platform: linux
  size: 56335986
  timestamp: 1661544618036
- kind: conda
  name: libcurl
  version: 8.7.1
  build: hca28451_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libcurl-8.7.1-hca28451_0.conda
  sha256: 82a75e9a5d9ee5b2f487d850ec5d4edc18a56eb9527608a95a916c40baae3843
  md5: 755c7f876815003337d2c61ff5d047e5
  depends:
  - krb5 >=1.21.2,<1.22.0a0
  - libgcc-ng >=12
  - libnghttp2 >=1.58.0,<2.0a0
  - libssh2 >=1.11.0,<2.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - openssl >=3.2.1,<4.0a0
  - zstd >=1.5.5,<1.6.0a0
  license: curl
  license_family: MIT
  size: 398293
  timestamp: 1711548114077
- kind: conda
  name: libcusolver
  version: *********
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcusolver-*********-0.tar.bz2
  md5: a497123295be4e0bd221da5bf215f8b8
  arch: x86_64
  platform: linux
  size: 101143771
  timestamp: 1661488678855
- kind: conda
  name: libcusolver-dev
  version: *********
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcusolver-dev-*********-0.tar.bz2
  md5: eef8b2209666925de40dc52dc2bd0036
  depends:
  - libcusolver >=*********
  arch: x86_64
  platform: linux
  size: 69542891
  timestamp: 1661488763689
- kind: conda
  name: libcusparse
  version: *********
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcusparse-*********-0.tar.bz2
  md5: 853c37fabd07b5b91d3007afc82c3ed4
  arch: x86_64
  platform: linux
  size: 184891885
  timestamp: 1661489463016
- kind: conda
  name: libcusparse-dev
  version: *********
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libcusparse-dev-*********-0.tar.bz2
  md5: e0c8805b9b08087d1cce37bcc270a3e4
  depends:
  - libcusparse >=*********
  arch: x86_64
  platform: linux
  size: 377165256
  timestamp: 1661489561708
- kind: conda
  name: libdeflate
  version: '1.20'
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.20-hd590300_0.conda
  sha256: f8e0f25c382b1d0b87a9b03887a34dbd91485453f1ea991fef726dba57373612
  md5: 8e88f9389f1165d7c0936fe40d9a9a79
  depends:
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 71500
  timestamp: 1711196523408
- kind: conda
  name: libdrm
  version: 2.4.120
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libdrm-2.4.120-hd590300_0.conda
  sha256: 8622f52e517418ae7234081fac14a3caa8aec5d1ee5f881ca1f3b194d81c3150
  md5: 7c3071bdf1d28b331a06bda6e85ab607
  depends:
  - libgcc-ng >=12
  - libpciaccess >=0.18,<0.19.0a0
  license: MIT
  license_family: MIT
  size: 303067
  timestamp: 1708374304366
- kind: conda
  name: libedit
  version: 3.1.20191231
  build: he28a2e2_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20191231-he28a2e2_2.tar.bz2
  sha256: a57d37c236d8f7c886e01656f4949d9dcca131d2a0728609c6f7fa338b65f1cf
  md5: 4d331e44109e3f0e19b4cb8f9b82f3e1
  depends:
  - libgcc-ng >=7.5.0
  - ncurses >=6.2,<7.0.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 123878
  timestamp: 1597616541093
- kind: conda
  name: libev
  version: '4.33'
  build: hd590300_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libev-4.33-hd590300_2.conda
  sha256: 1cd6048169fa0395af74ed5d8f1716e22c19a81a8a36f934c110ca3ad4dd27b4
  md5: 172bf1cd1ff8629f2b1179945ed45055
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 112766
  timestamp: 1702146165126
- kind: conda
  name: libevent
  version: 2.1.12
  build: hf998b51_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libevent-2.1.12-hf998b51_1.conda
  sha256: 2e14399d81fb348e9d231a82ca4d816bf855206923759b69ad006ba482764131
  md5: a1cfcc585f0c42bf8d5546bb1dfb668d
  depends:
  - libgcc-ng >=12
  - openssl >=3.1.1,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 427426
  timestamp: 1685725977222
- kind: conda
  name: libexpat
  version: 2.6.2
  build: h59595ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.6.2-h59595ed_0.conda
  sha256: 331bb7c7c05025343ebd79f86ae612b9e1e74d2687b8f3179faec234f986ce19
  md5: e7ba12deb7020dd080c6c70e7b6f6a3d
  depends:
  - libgcc-ng >=12
  constrains:
  - expat 2.6.2.*
  license: MIT
  license_family: MIT
  size: 73730
  timestamp: 1710362120304
- kind: conda
  name: libffi
  version: 3.4.2
  build: h7f98852_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.2-h7f98852_5.tar.bz2
  sha256: ab6e9856c21709b7b517e940ae7028ae0737546122f83c2aa5d692860c3b149e
  md5: d645c6d2ac96843a2bfaccd2d62b3ac3
  depends:
  - libgcc-ng >=9.4.0
  license: MIT
  license_family: MIT
  size: 58292
  timestamp: 1636488182923
- kind: conda
  name: libflac
  version: 1.4.3
  build: h59595ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libflac-1.4.3-h59595ed_0.conda
  sha256: 65908b75fa7003167b8a8f0001e11e58ed5b1ef5e98b96ab2ba66d7c1b822c7d
  md5: ee48bf17cc83a00f59ca1494d5646869
  depends:
  - gettext >=0.21.1,<1.0a0
  - libgcc-ng >=12
  - libogg 1.3.*
  - libogg >=1.3.4,<1.4.0a0
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 394383
  timestamp: 1687765514062
- kind: conda
  name: libgcc-ng
  version: 13.2.0
  build: h77fa898_7
  build_number: 7
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-13.2.0-h77fa898_7.conda
  sha256: 62af2b89acbe74a21606c8410c276e57309c0a2ab8a9e8639e3c8131c0b60c92
  md5: 72ec1b1b04c4d15d4204ece1ecea5978
  depends:
  - _libgcc_mutex 0.1 conda_forge
  - _openmp_mutex >=4.5
  constrains:
  - libgomp 13.2.0 h77fa898_7
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 775806
  timestamp: 1715016057793
- kind: conda
  name: libgcrypt
  version: 1.10.3
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libgcrypt-1.10.3-hd590300_0.conda
  sha256: d1bd47faa29fec7288c7b212198432b07f890d3d6f646078da93b059c2e9daff
  md5: 32d16ad533c59bb0a3c5ffaf16110829
  depends:
  - libgcc-ng >=12
  - libgpg-error >=1.47,<2.0a0
  license: LGPL-2.1-or-later AND GPL-2.0-or-later
  license_family: GPL
  size: 634887
  timestamp: 1701383493365
- kind: conda
  name: libgettextpo
  version: 0.22.5
  build: h59595ed_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-0.22.5-h59595ed_2.conda
  sha256: e2f784564a2bdc6f753f00f63cc77c97601eb03bc89dccc4413336ec6d95490b
  md5: 172bcc51059416e7ce99e7b528cede83
  depends:
  - libgcc-ng >=12
  license: GPL-3.0-or-later
  license_family: GPL
  size: 170582
  timestamp: 1712512286907
- kind: conda
  name: libgettextpo-devel
  version: 0.22.5
  build: h59595ed_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-devel-0.22.5-h59595ed_2.conda
  sha256: 695eb2439ad4a89e4205dd675cc52fba5cef6b5d41b83f07cdbf4770a336cc15
  md5: b63d9b6da3653179a278077f0de20014
  depends:
  - libgcc-ng >=12
  - libgettextpo 0.22.5 h59595ed_2
  license: GPL-3.0-or-later
  license_family: GPL
  size: 36758
  timestamp: 1712512303244
- kind: conda
  name: libgfortran-ng
  version: 13.2.0
  build: h69a702a_7
  build_number: 7
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-ng-13.2.0-h69a702a_7.conda
  sha256: a588e69f96b8e0983a8cdfdbf1dc75eb48189f5420ec71150c8d8cdc0a811a9b
  md5: 1b84f26d9f4f6026e179e7805d5a15cd
  depends:
  - libgfortran5 13.2.0 hca663fb_7
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 24314
  timestamp: 1715016272844
- kind: conda
  name: libgfortran5
  version: 13.2.0
  build: hca663fb_7
  build_number: 7
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-13.2.0-hca663fb_7.conda
  sha256: 754ab038115edce550fdccdc9ddf7dead2fa8346b8cdd4428c59ae1e83293978
  md5: c0bd771f09a326fdcd95a60b617795bf
  depends:
  - libgcc-ng >=13.2.0
  constrains:
  - libgfortran-ng 13.2.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 1441361
  timestamp: 1715016068766
- kind: conda
  name: libglib
  version: 2.80.0
  build: hf2295e7_6
  build_number: 6
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.80.0-hf2295e7_6.conda
  sha256: d2867a1515676f3b64265420598badb2e4ad2369d85237fb276173a99959eb37
  md5: 9342e7c44c38bea649490f72d92c382d
  depends:
  - libffi >=3.4,<4.0a0
  - libgcc-ng >=12
  - libiconv >=1.17,<2.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - pcre2 >=10.43,<10.44.0a0
  constrains:
  - glib 2.80.0 *_6
  license: LGPL-2.1-or-later
  size: 3942450
  timestamp: 1713639388280
- kind: conda
  name: libglu
  version: 9.0.0
  build: hac7e632_1003
  build_number: 1003
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libglu-9.0.0-hac7e632_1003.conda
  sha256: 8368435c41105dc3e1c02896a02ecaa21b77d0b0d67fc8b06a16ba885c86f917
  md5: 50c389a09b6b7babaef531eb7cb5e0ca
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libxcb >=1.15,<1.16.0a0
  - xorg-libx11 >=1.8.6,<2.0a0
  - xorg-libxext >=1.3.4,<2.0a0
  - xorg-xextproto >=7.3.0,<8.0a0
  license: SGI-2
  size: 331249
  timestamp: 1694431884320
- kind: conda
  name: libgoogle-cloud
  version: 2.23.0
  build: h9be4e54_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-2.23.0-h9be4e54_1.conda
  sha256: 680f5a9bc45aa905d9da086b16551438553649e05dd6b94b02b379b050602d5e
  md5: 1042d8401bb268553f98e60120cdeb40
  depends:
  - libabseil * cxx17*
  - libabseil >=20240116.1,<20240117.0a0
  - libcurl >=8.7.1,<9.0a0
  - libgcc-ng >=12
  - libgrpc >=1.62.2,<1.63.0a0
  - libprotobuf >=4.25.3,<4.25.4.0a0
  - libstdcxx-ng >=12
  - openssl >=3.2.1,<4.0a0
  constrains:
  - libgoogle-cloud 2.23.0 *_1
  license: Apache-2.0
  license_family: Apache
  size: 1214608
  timestamp: 1713798219648
- kind: conda
  name: libgoogle-cloud-storage
  version: 2.23.0
  build: hc7a4891_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-storage-2.23.0-hc7a4891_1.conda
  sha256: b85ce8b78e9262670a145a1639e253708e2a9eb9100d60ccec16f8e41d87a4bb
  md5: ee99fb9107ffb579b58ee92a5fb14b06
  depends:
  - libabseil
  - libcrc32c >=1.1.2,<1.2.0a0
  - libcurl
  - libgcc-ng >=12
  - libgoogle-cloud 2.23.0 h9be4e54_1
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - openssl
  license: Apache-2.0
  license_family: Apache
  size: 752661
  timestamp: 1713798390317
- kind: conda
  name: libgpg-error
  version: '1.49'
  build: h4f305b6_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libgpg-error-1.49-h4f305b6_0.conda
  sha256: b2664c2c11211a63856f23278efb49d3e65d902297989a0c12dcd228b5d97110
  md5: dfcfd72c7a430d3616763ecfbefe4ca9
  depends:
  - gettext
  - libasprintf >=0.22.5,<1.0a0
  - libgcc-ng >=12
  - libgettextpo >=0.22.5,<1.0a0
  - libstdcxx-ng >=12
  license: GPL-2.0-only
  license_family: GPL
  size: 263319
  timestamp: 1714121531915
- kind: conda
  name: libgrpc
  version: 1.62.2
  build: h15f2491_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libgrpc-1.62.2-h15f2491_0.conda
  sha256: 28241ed89335871db33cb6010e9ccb2d9e9b6bb444ddf6884f02f0857363c06a
  md5: 8dabe607748cb3d7002ad73cd06f1325
  depends:
  - c-ares >=1.28.1,<2.0a0
  - libabseil * cxx17*
  - libabseil >=20240116.1,<20240117.0a0
  - libgcc-ng >=12
  - libprotobuf >=4.25.3,<4.25.4.0a0
  - libre2-11 >=2023.9.1,<2024.0a0
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - openssl >=3.2.1,<4.0a0
  - re2
  constrains:
  - grpc-cpp =1.62.2
  license: Apache-2.0
  license_family: APACHE
  size: 7316832
  timestamp: 1713390645548
- kind: conda
  name: libhwloc
  version: 2.10.0
  build: default_h2fb2949_1000
  build_number: 1000
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libhwloc-2.10.0-default_h2fb2949_1000.conda
  sha256: dab61dff22f40367e57b1fe024e789f451b7511e65c32b97ada97ca549dd8dbc
  md5: 7e3726e647a619c6ce5939014dfde86d
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libxml2 >=2.12.6,<3.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 2412713
  timestamp: 1711490522117
- kind: conda
  name: libiconv
  version: '1.17'
  build: hd590300_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.17-hd590300_2.conda
  sha256: 8ac2f6a9f186e76539439e50505d98581472fedb347a20e7d1f36429849f05c9
  md5: d66573916ffcf376178462f1b61c941e
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-only
  size: 705775
  timestamp: 1702682170569
- kind: conda
  name: libidn2
  version: 2.3.7
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libidn2-2.3.7-hd590300_0.conda
  sha256: 253f9be445c58bf07b39d8f67ac08bccc5010c75a8c2070cddfb6c20e1ca4f4f
  md5: 2b7b0d827c6447cc1d85dc06d5b5de46
  depends:
  - gettext >=0.21.1,<1.0a0
  - libgcc-ng >=12
  - libunistring >=0,<1.0a0
  license: LGPLv2
  size: 126515
  timestamp: 1706368269716
- kind: conda
  name: libjpeg-turbo
  version: 3.0.0
  build: hd590300_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.0.0-hd590300_1.conda
  sha256: b954e09b7e49c2f2433d6f3bb73868eda5e378278b0f8c1dd10a7ef090e14f2f
  md5: ea25936bb4080d843790b586850f82b8
  depends:
  - libgcc-ng >=12
  constrains:
  - jpeg <0.0.0a
  license: IJG AND BSD-3-Clause AND Zlib
  size: 618575
  timestamp: 1694474974816
- kind: conda
  name: liblapack
  version: 3.9.0
  build: 5_h92ddd45_netlib
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-5_h92ddd45_netlib.tar.bz2
  sha256: 41eb83ea6ce409fd8c81fc38e029422840f581022e6f33998a680c0b23884cd6
  md5: ffb80081cf8f43903b07045630188851
  depends:
  - libblas 3.9.0.*
  - libgcc-ng >=9.3.0
  - libgfortran-ng
  - libgfortran5 >=9.3.0
  track_features:
  - blas_netlib
  license: BSD-3-Clause
  license_family: BSD
  size: 3111106
  timestamp: 1618011727809
- kind: conda
  name: libllvm15
  version: 15.0.7
  build: hb3ce162_4
  build_number: 4
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libllvm15-15.0.7-hb3ce162_4.conda
  sha256: e71584c0f910140630580fdd0a013029a52fd31e435192aea2aa8d29005262d1
  md5: 8a35df3cbc0c8b12cc8af9473ae75eef
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libxml2 >=2.12.1,<3.0.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - zstd >=1.5.5,<1.6.0a0
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  size: 33321457
  timestamp: 1701375836233
- kind: conda
  name: libllvm16
  version: 16.0.6
  build: hb3ce162_3
  build_number: 3
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libllvm16-16.0.6-hb3ce162_3.conda
  sha256: 624fa4012397bc5a8c9269247bf9baa7d907eb59079aefc6f6fa6a40f10fd0ba
  md5: a4d48c40dd5c60edbab7fd69c9a88967
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libxml2 >=2.12.1,<3.0.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - zstd >=1.5.5,<1.6.0a0
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  size: 35359734
  timestamp: 1701375139881
- kind: conda
  name: libllvm18
  version: 18.1.5
  build: hb77312f_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libllvm18-18.1.5-hb77312f_0.conda
  sha256: 2e0a7c023b2df11bd316baad7c409bc95f2e7a92a322ab7973c08d72d03653d2
  md5: efd221d3668077ca067a206269418dec
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libxml2 >=2.12.6,<3.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - zstd >=1.5.6,<1.6.0a0
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  size: 38403202
  timestamp: 1714775293919
- kind: conda
  name: libnghttp2
  version: 1.58.0
  build: h47da74e_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libnghttp2-1.58.0-h47da74e_1.conda
  sha256: 1910c5306c6aa5bcbd623c3c930c440e9c77a5a019008e1487810e3c1d3716cb
  md5: 700ac6ea6d53d5510591c4344d5c989a
  depends:
  - c-ares >=1.23.0,<2.0a0
  - libev >=4.33,<4.34.0a0
  - libev >=4.33,<5.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - openssl >=3.2.0,<4.0a0
  license: MIT
  license_family: MIT
  size: 631936
  timestamp: 1702130036271
- kind: conda
  name: libnl
  version: 3.9.0
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libnl-3.9.0-hd590300_0.conda
  sha256: aae03117811e704c3f3666e8374dd2e632f1d78bef0c27330e7298b24004819e
  md5: d27c451db4f1d3c983c78167d2fdabc2
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-or-later
  license_family: LGPL
  size: 732866
  timestamp: 1702657849946
- kind: conda
  name: libnpp
  version: *********
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libnpp-*********-0.tar.bz2
  md5: 03822c4b5dae5988ba9dcb7eae837345
  arch: x86_64
  platform: linux
  size: 154995740
  timestamp: 1661489365078
- kind: conda
  name: libnpp-dev
  version: *********
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libnpp-dev-*********-0.tar.bz2
  md5: dde377399a27bb87e124db9c8b898e32
  depends:
  - libnpp >=*********
  arch: x86_64
  platform: linux
  size: 151472361
  timestamp: 1661489433906
- kind: conda
  name: libnsl
  version: 2.0.1
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hd590300_0.conda
  sha256: 26d77a3bb4dceeedc2a41bd688564fe71bf2d149fdcf117049970bc02ff1add6
  md5: 30fd6e37fe21f86f4bd26d6ee73eeec7
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-only
  license_family: GPL
  size: 33408
  timestamp: 1697359010159
- kind: conda
  name: libnvjpeg
  version: *********
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libnvjpeg-*********-0.tar.bz2
  md5: e42d6f0f20feb0cba0165d5cae33362f
  arch: x86_64
  platform: linux
  size: 2508741
  timestamp: 1661545915358
- kind: conda
  name: libnvjpeg-dev
  version: *********
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/libnvjpeg-dev-*********-0.tar.bz2
  md5: 3fa3184a0180608546e54934be2e4a26
  depends:
  - libnvjpeg >=*********
  arch: x86_64
  platform: linux
  size: 2166145
  timestamp: 1661545917152
- kind: conda
  name: libogg
  version: 1.3.4
  build: h7f98852_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libogg-1.3.4-h7f98852_1.tar.bz2
  sha256: b88afeb30620b11bed54dac4295aa57252321446ba4e6babd7dce4b9ffde9b25
  md5: 6e8cc2173440d77708196c5b93771680
  depends:
  - libgcc-ng >=9.3.0
  license: BSD-3-Clause
  license_family: BSD
  size: 210550
  timestamp: 1610382007814
- kind: conda
  name: libopenvino
  version: 2024.0.0
  build: h2da1b83_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-2024.0.0-h2da1b83_5.conda
  sha256: ef0f39b7378663c296cddc184f0a028d6a5178fc61d145fd9407f635edb15de7
  md5: 87d1f91d897ed6678a615536a25fd13f
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - pugixml >=1.14,<1.15.0a0
  - tbb >=2021.11.0
  size: 5113894
  timestamp: 1712674184348
- kind: conda
  name: libopenvino-auto-batch-plugin
  version: 2024.0.0
  build: hb045406_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-auto-batch-plugin-2024.0.0-hb045406_5.conda
  sha256: 51aff831cbb65ee29ddd977c66a5a0224b2580ea7fe131885054bc8effa74e4f
  md5: ddbb87c004506e586b79b674f0696aa3
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libopenvino 2024.0.0 h2da1b83_5
  - libstdcxx-ng >=12
  - tbb >=2021.11.0
  size: 110915
  timestamp: 1712674229190
- kind: conda
  name: libopenvino-auto-plugin
  version: 2024.0.0
  build: hb045406_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-auto-plugin-2024.0.0-hb045406_5.conda
  sha256: 63c26a03ea8953cccde1302b26520ef53dccce6d1e234c25fc249c1ea75f14d1
  md5: 59aad82eda612aa46ba300576d536966
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libopenvino 2024.0.0 h2da1b83_5
  - libstdcxx-ng >=12
  - tbb >=2021.11.0
  size: 228961
  timestamp: 1712674245147
- kind: conda
  name: libopenvino-hetero-plugin
  version: 2024.0.0
  build: h5c03a75_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-hetero-plugin-2024.0.0-h5c03a75_5.conda
  sha256: 41ab26306eeadc35b1765ae49ea651429e39239e51140cbe695ecd7e5111d9c9
  md5: c69a1ed3e89dc16c7e1df30ed1fd73f7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libopenvino 2024.0.0 h2da1b83_5
  - libstdcxx-ng >=12
  - pugixml >=1.14,<1.15.0a0
  size: 179737
  timestamp: 1712674261368
- kind: conda
  name: libopenvino-intel-cpu-plugin
  version: 2024.0.0
  build: h2da1b83_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-cpu-plugin-2024.0.0-h2da1b83_5.conda
  sha256: 41a31574f67e01e012ff7691c2bf2bcf24298307991f9e8b04b756bf0cd42be3
  md5: deb11c7339478aba1b5b7cf15c443396
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libopenvino 2024.0.0 h2da1b83_5
  - libstdcxx-ng >=12
  - pugixml >=1.14,<1.15.0a0
  - tbb >=2021.11.0
  size: 10628363
  timestamp: 1712674280576
- kind: conda
  name: libopenvino-intel-gpu-plugin
  version: 2024.0.0
  build: h2da1b83_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-gpu-plugin-2024.0.0-h2da1b83_5.conda
  sha256: 0d691749d38bf7a1b5f347336486c126af9591e427486aae92f465f6c0ea7d66
  md5: 1f5902112c12e9a8ae2bb4c51294d35a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libopenvino 2024.0.0 h2da1b83_5
  - libstdcxx-ng >=12
  - ocl-icd >=2.3.2,<3.0a0
  - pugixml >=1.14,<1.15.0a0
  - tbb >=2021.11.0
  size: 8353537
  timestamp: 1712674327020
- kind: conda
  name: libopenvino-ir-frontend
  version: 2024.0.0
  build: h5c03a75_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-ir-frontend-2024.0.0-h5c03a75_5.conda
  sha256: c1e8556f42cd001ee03298c6b044730daae2adcc1af035df0edccf8eb4dd5261
  md5: 3a5e6778c907c33503be9c051a6424ae
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libopenvino 2024.0.0 h2da1b83_5
  - libstdcxx-ng >=12
  - pugixml >=1.14,<1.15.0a0
  size: 200926
  timestamp: 1712674364843
- kind: conda
  name: libopenvino-onnx-frontend
  version: 2024.0.0
  build: h07e8aee_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-onnx-frontend-2024.0.0-h07e8aee_5.conda
  sha256: 68506cc32799197fecdceb50a220c8ad5092c0279192bd2c0ecc33f57beb2397
  md5: 5b9b0c983e7b14ba4764d75a9bf7a6f3
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libopenvino 2024.0.0 h2da1b83_5
  - libprotobuf >=4.25.3,<4.25.4.0a0
  - libstdcxx-ng >=12
  size: 1586243
  timestamp: 1712674383761
- kind: conda
  name: libopenvino-paddle-frontend
  version: 2024.0.0
  build: h07e8aee_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-paddle-frontend-2024.0.0-h07e8aee_5.conda
  sha256: 7ad02d506f4a67b37f1c3ec72c950d339c10c29a53a36326094fbbfa62ec3cd1
  md5: 77ba4135acc68fdade36cca31446c3e8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libopenvino 2024.0.0 h2da1b83_5
  - libprotobuf >=4.25.3,<4.25.4.0a0
  - libstdcxx-ng >=12
  size: 695337
  timestamp: 1712674402836
- kind: conda
  name: libopenvino-pytorch-frontend
  version: 2024.0.0
  build: he02047a_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-pytorch-frontend-2024.0.0-he02047a_5.conda
  sha256: 71795c64c5b6a853130fefb435376b836a6e42eac63117a03a78fa82d76f4af0
  md5: deb1f6397c3aa6dbb35d197499829623
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libopenvino 2024.0.0 h2da1b83_5
  - libstdcxx-ng >=12
  size: 1066396
  timestamp: 1712674419569
- kind: conda
  name: libopenvino-tensorflow-frontend
  version: 2024.0.0
  build: h39126c6_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-tensorflow-frontend-2024.0.0-h39126c6_5.conda
  sha256: d3302692d306eb59f99650393a5f3a7f9312bde5c2a3982432fb667ee4f1d89c
  md5: 737f0ee3a70c84758333318afa86d46e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20240116.1,<20240117.0a0
  - libgcc-ng >=12
  - libopenvino 2024.0.0 h2da1b83_5
  - libprotobuf >=4.25.3,<4.25.4.0a0
  - libstdcxx-ng >=12
  - snappy >=1.2.0,<1.3.0a0
  size: 1270703
  timestamp: 1712674438216
- kind: conda
  name: libopenvino-tensorflow-lite-frontend
  version: 2024.0.0
  build: he02047a_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-tensorflow-lite-frontend-2024.0.0-he02047a_5.conda
  sha256: 33d537f0a7d43e384e9808191f79ae2115213ff78d291bab3d57db9d9c66683a
  md5: 5161e70dfbcd31a6e28b5d75c0b620e6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libopenvino 2024.0.0 h2da1b83_5
  - libstdcxx-ng >=12
  size: 477616
  timestamp: 1712674457796
- kind: conda
  name: libopus
  version: 1.3.1
  build: h7f98852_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libopus-1.3.1-h7f98852_1.tar.bz2
  sha256: 0e1c2740ebd1c93226dc5387461bbcf8142c518f2092f3ea7551f77755decc8f
  md5: 15345e56d527b330e1cacbdf58676e8f
  depends:
  - libgcc-ng >=9.3.0
  license: BSD-3-Clause
  license_family: BSD
  size: 260658
  timestamp: 1606823578035
- kind: conda
  name: libparquet
  version: 15.0.2
  build: hacf5a1f_6_cpu
  build_number: 6
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libparquet-15.0.2-hacf5a1f_6_cpu.conda
  sha256: ac842454e033b8ad638eec0e0672db8e82d7a53d6c5f61ec93badcee02373966
  md5: 37d4b8f700247904e94d8fb0a90e488e
  depends:
  - gflags >=2.2.2,<2.3.0a0
  - libarrow 15.0.2 hefa796f_6_cpu
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libthrift >=0.19.0,<0.19.1.0a0
  - openssl >=3.2.1,<4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 1181574
  timestamp: 1714448276294
- kind: conda
  name: libpciaccess
  version: '0.18'
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libpciaccess-0.18-hd590300_0.conda
  sha256: c0a30ac74eba66ea76a4f0a39acc7833f5ed783a632ca3bb6665b2d81aabd2fb
  md5: 48f4330bfcd959c3cfb704d424903c82
  depends:
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 28361
  timestamp: 1707101388552
- kind: conda
  name: libpng
  version: 1.6.43
  build: h2797004_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.43-h2797004_0.conda
  sha256: 502f6ff148ac2777cc55ae4ade01a8fc3543b4ffab25c4e0eaa15f94e90dd997
  md5: 009981dd9cfcaa4dbfa25ffaed86bcae
  depends:
  - libgcc-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  license: zlib-acknowledgement
  size: 288221
  timestamp: 1708780443939
- kind: conda
  name: libpq
  version: '16.2'
  build: h33b98f1_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libpq-16.2-h33b98f1_1.conda
  sha256: e03a8439b79e013840c44c957d37dbce10316888b2b5dc7dcfcfc0cfe3a3b128
  md5: 9e49ec2a61d02623b379dc332eb6889d
  depends:
  - krb5 >=1.21.2,<1.22.0a0
  - libgcc-ng >=12
  - openssl >=3.2.1,<4.0a0
  license: PostgreSQL
  size: 2601973
  timestamp: 1710863646063
- kind: conda
  name: libprotobuf
  version: 4.25.3
  build: h08a7969_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libprotobuf-4.25.3-h08a7969_0.conda
  sha256: 70e0eef046033af2e8d21251a785563ad738ed5281c74e21c31c457780845dcd
  md5: 6945825cebd2aeb16af4c69d97c32c13
  depends:
  - libabseil * cxx17*
  - libabseil >=20240116.1,<20240117.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 2811207
  timestamp: 1709514552541
- kind: conda
  name: libraw
  version: 0.21.1
  build: h2a13503_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libraw-0.21.1-h2a13503_2.conda
  sha256: a23ab9470bbf0ae0505b2991f139085e0a99b32f8640a22d3c540b515c352301
  md5: 63ab3e0cf149917a08af38b2786320c0
  depends:
  - _openmp_mutex >=4.5
  - lcms2 >=2.15,<3.0a0
  - libgcc-ng >=12
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  license: LGPL-2.1-only
  license_family: LGPL
  size: 637871
  timestamp: 1695983515562
- kind: conda
  name: libre2-11
  version: 2023.09.01
  build: h5a48ba9_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libre2-11-2023.09.01-h5a48ba9_2.conda
  sha256: 3f3c65fe0e9e328b4c1ebc2b622727cef3e5b81b18228cfa6cf0955bc1ed8eff
  md5: 41c69fba59d495e8cf5ffda48a607e35
  depends:
  - libabseil * cxx17*
  - libabseil >=20240116.1,<20240117.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  constrains:
  - re2 2023.09.01.*
  license: BSD-3-Clause
  license_family: BSD
  size: 232603
  timestamp: 1708946763521
- kind: conda
  name: libsndfile
  version: 1.2.2
  build: hc60ed4a_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libsndfile-1.2.2-hc60ed4a_1.conda
  sha256: f709cbede3d4f3aee4e2f8d60bd9e256057f410bd60b8964cb8cf82ec1457573
  md5: ef1910918dd895516a769ed36b5b3a4e
  depends:
  - lame >=3.100,<3.101.0a0
  - libflac >=1.4.3,<1.5.0a0
  - libgcc-ng >=12
  - libogg >=1.3.4,<1.4.0a0
  - libopus >=1.3.1,<2.0a0
  - libstdcxx-ng >=12
  - libvorbis >=1.3.7,<1.4.0a0
  - mpg123 >=1.32.1,<1.33.0a0
  license: LGPL-2.1-or-later
  license_family: LGPL
  size: 354372
  timestamp: 1695747735668
- kind: conda
  name: libsqlite
  version: 3.45.3
  build: h2797004_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.45.3-h2797004_0.conda
  sha256: e2273d6860eadcf714a759ffb6dc24a69cfd01f2a0ea9d6c20f86049b9334e0c
  md5: b3316cbe90249da4f8e84cd66e1cc55b
  depends:
  - libgcc-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  license: Unlicense
  size: 859858
  timestamp: 1713367435849
- kind: conda
  name: libssh2
  version: 1.11.0
  build: h0841786_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libssh2-1.11.0-h0841786_0.conda
  sha256: 50e47fd9c4f7bf841a11647ae7486f65220cfc988ec422a4475fe8d5a823824d
  md5: 1f5a58e686b13bcfde88b93f547d23fe
  depends:
  - libgcc-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - openssl >=3.1.1,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 271133
  timestamp: 1685837707056
- kind: conda
  name: libstdcxx-ng
  version: 13.2.0
  build: hc0a3c3a_7
  build_number: 7
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-13.2.0-hc0a3c3a_7.conda
  sha256: 35f1e08be0a84810c9075f5bd008495ac94e6c5fe306dfe4b34546f11fed850f
  md5: 53ebd4c833fa01cb2c6353e99f905406
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 3837704
  timestamp: 1715016117360
- kind: conda
  name: libsystemd0
  version: '255'
  build: h3516f8a_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libsystemd0-255-h3516f8a_1.conda
  sha256: af27b0d225435d03f378a119f8eab6b280c53557a3c84cdb3bb8fd3167615aed
  md5: 3366af27f0b593544a6cd453c7932ac5
  depends:
  - __glibc >=2.17,<3.0.a0
  - libcap >=2.69,<2.70.0a0
  - libgcc-ng >=12
  - libgcrypt >=1.10.3,<2.0a0
  - lz4-c >=1.9.3,<1.10.0a0
  - xz >=5.2.6,<6.0a0
  - zstd >=1.5.5,<1.6.0a0
  license: LGPL-2.1-or-later
  size: 402592
  timestamp: 1709568499820
- kind: conda
  name: libtasn1
  version: 4.19.0
  build: h166bdaf_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libtasn1-4.19.0-h166bdaf_0.tar.bz2
  sha256: 5bfeada0e1c6ec2574afe2d17cdbc39994d693a41431338a6cb9dfa7c4d7bfc8
  md5: 93840744a8552e9ebf6bb1a5dffc125a
  depends:
  - libgcc-ng >=12
  license: GPL-3.0-or-later
  license_family: GPL
  size: 116878
  timestamp: 1661325701583
- kind: conda
  name: libthrift
  version: 0.19.0
  build: hb90f79a_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libthrift-0.19.0-hb90f79a_1.conda
  sha256: 719add2cf20d144ef9962c57cd0f77178259bdb3aae1cded2e2b2b7c646092f5
  md5: 8cdb7d41faa0260875ba92414c487e2d
  depends:
  - libevent >=2.1.12,<2.1.13.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - openssl >=3.1.3,<4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 409409
  timestamp: 1695958011498
- kind: conda
  name: libtiff
  version: 4.6.0
  build: h1dd3fc0_3
  build_number: 3
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.6.0-h1dd3fc0_3.conda
  sha256: fc3b210f9584a92793c07396cb93e72265ff3f1fa7ca629128bf0a50d5cb15e4
  md5: 66f03896ffbe1a110ffda05c7a856504
  depends:
  - lerc >=4.0.0,<5.0a0
  - libdeflate >=1.20,<1.21.0a0
  - libgcc-ng >=12
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libstdcxx-ng >=12
  - libwebp-base >=1.3.2,<2.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - xz >=5.2.6,<6.0a0
  - zstd >=1.5.5,<1.6.0a0
  license: HPND
  size: 282688
  timestamp: 1711217970425
- kind: conda
  name: libunistring
  version: 0.9.10
  build: h7f98852_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libunistring-0.9.10-h7f98852_0.tar.bz2
  sha256: e88c45505921db29c08df3439ddb7f771bbff35f95e7d3103bf365d5d6ce2a6d
  md5: 7245a044b4a1980ed83196176b78b73a
  depends:
  - libgcc-ng >=9.3.0
  license: GPL-3.0-only OR LGPL-3.0-only
  size: 1433436
  timestamp: 1626955018689
- kind: conda
  name: libutf8proc
  version: 2.8.0
  build: h166bdaf_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libutf8proc-2.8.0-h166bdaf_0.tar.bz2
  sha256: 49082ee8d01339b225f7f8c60f32a2a2c05fe3b16f31b554b4fb2c1dea237d1c
  md5: ede4266dc02e875fe1ea77b25dd43747
  depends:
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 101070
  timestamp: 1667316029302
- kind: conda
  name: libuuid
  version: 2.38.1
  build: h0b41bf4_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
  sha256: 787eb542f055a2b3de553614b25f09eefb0a0931b0c87dbcce6efdfd92f04f18
  md5: 40b61aab5c7ba9ff276c41cfffe6b80b
  depends:
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 33601
  timestamp: 1680112270483
- kind: conda
  name: libva
  version: 2.21.0
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libva-2.21.0-hd590300_0.conda
  sha256: b4e3a3fa523a5ddd1eca7981c9d6a9b831a182950116cc5bda18c94a040b63bc
  md5: e50a2609159a3e336fe4092738c00687
  depends:
  - libdrm >=2.4.120,<2.5.0a0
  - xorg-libx11 >=1.8.7,<2.0a0
  - xorg-libxext >=1.3.4,<2.0a0
  - xorg-libxfixes
  license: MIT
  license_family: MIT
  size: 189313
  timestamp: 1710242676665
- kind: conda
  name: libvorbis
  version: 1.3.7
  build: h9c3ff4c_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libvorbis-1.3.7-h9c3ff4c_0.tar.bz2
  sha256: 53080d72388a57b3c31ad5805c93a7328e46ff22fab7c44ad2a86d712740af33
  md5: 309dec04b70a3cc0f1e84a4013683bc0
  depends:
  - libgcc-ng >=9.3.0
  - libogg >=1.3.4,<1.4.0a0
  - libstdcxx-ng >=9.3.0
  license: BSD-3-Clause
  license_family: BSD
  size: 286280
  timestamp: 1610609811627
- kind: conda
  name: libvpx
  version: 1.14.0
  build: h59595ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libvpx-1.14.0-h59595ed_0.conda
  sha256: b0e0500fc92f626baaa2cf926dece5ce7571c42a2db2d993a250d4c5da4d68ca
  md5: 01c76c6d71097a0f3bd8683a8f255123
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 1019593
  timestamp: 1707175376125
- kind: conda
  name: libwebp-base
  version: 1.4.0
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.4.0-hd590300_0.conda
  sha256: 49bc5f6b1e11cb2babf2a2a731d1a680a5e08a858280876a779dbda06c78c35f
  md5: b26e8aa824079e1be0294e7152ca4559
  depends:
  - libgcc-ng >=12
  constrains:
  - libwebp 1.4.0
  license: BSD-3-Clause
  license_family: BSD
  size: 438953
  timestamp: 1713199854503
- kind: conda
  name: libxcb
  version: '1.15'
  build: h0b41bf4_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.15-h0b41bf4_0.conda
  sha256: a670902f0a3173a466c058d2ac22ca1dd0df0453d3a80e0212815c20a16b0485
  md5: 33277193f5b92bad9fdd230eb700929c
  depends:
  - libgcc-ng >=12
  - pthread-stubs
  - xorg-libxau
  - xorg-libxdmcp
  license: MIT
  license_family: MIT
  size: 384238
  timestamp: 1682082368177
- kind: conda
  name: libxcrypt
  version: 4.4.36
  build: hd590300_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libxcrypt-4.4.36-hd590300_1.conda
  sha256: 6ae68e0b86423ef188196fff6207ed0c8195dd84273cb5623b85aa08033a410c
  md5: 5aa797f8787fe7a17d1b0821485b5adc
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-or-later
  size: 100393
  timestamp: 1702724383534
- kind: conda
  name: libxkbcommon
  version: 1.7.0
  build: h662e7e4_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libxkbcommon-1.7.0-h662e7e4_0.conda
  sha256: 3d97d7f964237f42452295d461afdbc51e93f72e2c80be516f56de80e3bb6621
  md5: b32c0da42b1f24a98577bb3d7fc0b995
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libxcb >=1.15,<1.16.0a0
  - libxml2 >=2.12.6,<3.0a0
  - xkeyboard-config
  - xorg-libxau >=1.0.11,<2.0a0
  license: MIT/X11 Derivative
  license_family: MIT
  size: 593534
  timestamp: 1711303445595
- kind: conda
  name: libxml2
  version: 2.12.6
  build: h232c23b_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.12.6-h232c23b_2.conda
  sha256: 0fd41df7211aae04f492c8550ce10238e8cfa8b1abebc2215a983c5e66d284ea
  md5: 9a3a42df8a95f65334dfc7b80da1195d
  depends:
  - icu >=73.2,<74.0a0
  - libgcc-ng >=12
  - libiconv >=1.17,<2.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - xz >=5.2.6,<6.0a0
  license: MIT
  license_family: MIT
  size: 704938
  timestamp: 1713314718258
- kind: conda
  name: libzlib
  version: 1.2.13
  build: hd590300_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.2.13-hd590300_5.conda
  sha256: 370c7c5893b737596fd6ca0d9190c9715d89d888b8c88537ae1ef168c25e82e4
  md5: f36c115f1ee199da648e0597ec2047ad
  depends:
  - libgcc-ng >=12
  constrains:
  - zlib 1.2.13 *_5
  license: Zlib
  license_family: Other
  size: 61588
  timestamp: 1686575217516
- kind: pypi
  name: lightning-utilities
  version: 0.11.2
  url: https://files.pythonhosted.org/packages/5e/9e/e7768a8e363fc6f0c978bb7a0aa7641f10d80be60000e788ef2f01d34a7c/lightning_utilities-0.11.2-py3-none-any.whl
  sha256: 541f471ed94e18a28d72879338c8c52e873bb46f4c47644d89228faeb6751159
  requires_dist:
  - packaging>=17.1
  - setuptools
  - typing-extensions
  - importlib-metadata>=4.0.0 ; python_version < '3.8'
  - fire ; extra == 'cli'
  - requests>=2.0.0 ; extra == 'docs'
  - mypy>=1.0.0 ; extra == 'typing'
  - types-setuptools ; extra == 'typing'
  requires_python: '>=3.8'
- kind: conda
  name: llvm-openmp
  version: 15.0.7
  build: h0cdce71_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/llvm-openmp-15.0.7-h0cdce71_0.conda
  sha256: 7c67d383a8b1f3e7bf9e046e785325c481f6868194edcfb9d78d261da4ad65d4
  md5: 589c9a3575a050b583241c3d688ad9aa
  depends:
  - libzlib >=1.2.13,<1.3.0a0
  constrains:
  - openmp 15.0.7|15.0.7.*
  license: Apache-2.0 WITH LLVM-exception
  license_family: APACHE
  size: 3268766
  timestamp: 1673584331056
- kind: pypi
  name: lxml
  version: 5.2.1
  url: https://files.pythonhosted.org/packages/61/8a/a0a71720da1c61fd6a55a1962ec1280cebc4552c319efe744c8aa99d28c5/lxml-5.2.1-cp310-cp310-manylinux_2_28_x86_64.whl
  sha256: a2b44bec7adf3e9305ce6cbfa47a4395667e744097faed97abb4728748ba7d47
  requires_dist:
  - cssselect>=0.7 ; extra == 'cssselect'
  - html5lib ; extra == 'html5'
  - lxml-html-clean ; extra == 'html-clean'
  - beautifulsoup4 ; extra == 'htmlsoup'
  - cython>=3.0.10 ; extra == 'source'
  requires_python: '>=3.6'
- kind: conda
  name: lz4-c
  version: 1.9.4
  build: hcb278e6_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/lz4-c-1.9.4-hcb278e6_0.conda
  sha256: 1b4c105a887f9b2041219d57036f72c4739ab9e9fe5a1486f094e58c76b31f5f
  md5: 318b08df404f9c9be5712aaa5a6f0bb0
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 143402
  timestamp: 1674727076728
- kind: pypi
  name: mapbox-earcut
  version: 1.0.1
  url: https://files.pythonhosted.org/packages/a9/6d/8b08b54435ee0b2b1d0d3b9a5e212cc42f4dce08cd6e2441b3b9d216471a/mapbox_earcut-1.0.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: 20929541c1c9f5fefde45c6c33e8ed3138c7bdd1034ced998877913878f3457c
  requires_dist:
  - numpy
  - pytest ; extra == 'test'
- kind: pypi
  name: markdown
  version: '3.6'
  url: https://files.pythonhosted.org/packages/fc/b3/0c0c994fe49cd661084f8d5dc06562af53818cc0abefaca35bdc894577c3/Markdown-3.6-py3-none-any.whl
  sha256: 48f276f4d8cfb8ce6527c8f79e2ee29708508bf4d40aa410fbc3b4ee832c850f
  requires_dist:
  - importlib-metadata>=4.4 ; python_version < '3.10'
  - mkdocs>=1.5 ; extra == 'docs'
  - mkdocs-nature>=0.6 ; extra == 'docs'
  - mdx-gh-links>=0.2 ; extra == 'docs'
  - mkdocstrings[python] ; extra == 'docs'
  - mkdocs-gen-files ; extra == 'docs'
  - mkdocs-section-index ; extra == 'docs'
  - mkdocs-literate-nav ; extra == 'docs'
  - coverage ; extra == 'testing'
  - pyyaml ; extra == 'testing'
  requires_python: '>=3.8'
- kind: pypi
  name: markdown-it-py
  version: 3.0.0
  url: https://files.pythonhosted.org/packages/42/d7/1ec15b46af6af88f19b8e5ffea08fa375d433c998b8a7639e76935c14f1f/markdown_it_py-3.0.0-py3-none-any.whl
  sha256: 355216845c60bd96232cd8d8c40e8f9765cc86f46880e43a8fd22dc1a1a8cab1
  requires_dist:
  - mdurl~=0.1
  - psutil ; extra == 'benchmarking'
  - pytest ; extra == 'benchmarking'
  - pytest-benchmark ; extra == 'benchmarking'
  - pre-commit~=3.0 ; extra == 'code-style'
  - commonmark~=0.9 ; extra == 'compare'
  - markdown~=3.4 ; extra == 'compare'
  - mistletoe~=1.0 ; extra == 'compare'
  - mistune~=2.0 ; extra == 'compare'
  - panflute~=2.3 ; extra == 'compare'
  - linkify-it-py>=1,<3 ; extra == 'linkify'
  - mdit-py-plugins ; extra == 'plugins'
  - gprof2dot ; extra == 'profiling'
  - mdit-py-plugins ; extra == 'rtd'
  - myst-parser ; extra == 'rtd'
  - pyyaml ; extra == 'rtd'
  - sphinx ; extra == 'rtd'
  - sphinx-copybutton ; extra == 'rtd'
  - sphinx-design ; extra == 'rtd'
  - sphinx-book-theme ; extra == 'rtd'
  - jupyter-sphinx ; extra == 'rtd'
  - coverage ; extra == 'testing'
  - pytest ; extra == 'testing'
  - pytest-cov ; extra == 'testing'
  - pytest-regressions ; extra == 'testing'
  requires_python: '>=3.8'
- kind: conda
  name: markupsafe
  version: 2.1.5
  build: py310h2372a71_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/markupsafe-2.1.5-py310h2372a71_0.conda
  sha256: 3c18347adf1d091ee9248612308a6bef79038f80b626ef67f58cd0e8d25c65b8
  md5: f6703fa0214a00bf49d1bef6dc7672d0
  depends:
  - libgcc-ng >=12
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  constrains:
  - jinja2 >=3.0.0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/markupsafe
  size: 24493
  timestamp: 1706900070478
- kind: pypi
  name: matplotlib
  version: 3.8.4
  url: https://files.pythonhosted.org/packages/d6/07/061f97211f942101070a46fecd813a6b1bd83590ed7b07c473cabd707fe7/matplotlib-3.8.4-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: ecd79298550cba13a43c340581a3ec9c707bd895a6a061a78fa2524660482fc0
  requires_dist:
  - contourpy>=1.0.1
  - cycler>=0.10
  - fonttools>=4.22.0
  - kiwisolver>=1.3.1
  - numpy>=1.21
  - packaging>=20.0
  - pillow>=8
  - pyparsing>=2.3.1
  - python-dateutil>=2.7
  - importlib-resources>=3.2.0 ; python_version < '3.10'
  requires_python: '>=3.9'
- kind: pypi
  name: matplotlib-inline
  version: 0.1.7
  url: https://files.pythonhosted.org/packages/8f/8e/9ad090d3553c280a8060fbf6e24dc1c0c29704ee7d1c372f0c174aa59285/matplotlib_inline-0.1.7-py3-none-any.whl
  sha256: df192d39a4ff8f21b1895d72e6a13f5fcc5099f00fa84384e0ea28c2cc0653ca
  requires_dist:
  - traitlets
  requires_python: '>=3.8'
- kind: pypi
  name: mdurl
  version: 0.1.2
  url: https://files.pythonhosted.org/packages/b3/38/89ba8ad64ae25be8de66a6d463314cf1eb366222074cfda9ee839c56a4b4/mdurl-0.1.2-py3-none-any.whl
  sha256: 84008a41e51615a49fc9966191ff91509e3c40b939176e643fd50a5c2196b8f8
  requires_python: '>=3.7'
- kind: pypi
  name: mediapy
  version: 1.2.0
  url: https://files.pythonhosted.org/packages/1a/26/583ff25923efd88c90733a0fad51890c04e3367663b55548d0c9ed0a658c/mediapy-1.2.0-py3-none-any.whl
  sha256: 7b69355d9aa4c513f41a642a9969899dcdc0303c4b20eea0be46a0226fb1fd14
  requires_dist:
  - ipython
  - matplotlib
  - numpy
  - pillow
  - absl-py ; extra == 'dev'
  - pyink ; extra == 'dev'
  - pylint>=2.6.0 ; extra == 'dev'
  - pytest ; extra == 'dev'
  - pytest-xdist ; extra == 'dev'
  - pytype ; extra == 'dev'
  requires_python: '>=3.8'
- kind: conda
  name: metis
  version: 5.1.0
  build: h59595ed_1007
  build_number: 1007
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/metis-5.1.0-h59595ed_1007.conda
  sha256: 446bf794497284e2ffa28ab9191d70c38d372c51e3fd073f0d8b35efb51e7e02
  md5: 40ccb8318df2500f83bd868dd8fcd201
  depends:
  - libgcc-ng >=12
  license: Apache-2.0
  license_family: APACHE
  size: 3890263
  timestamp: 1693402645559
- kind: pypi
  name: mistune
  version: 3.0.2
  url: https://files.pythonhosted.org/packages/f0/74/c95adcdf032956d9ef6c89a9b8a5152bf73915f8c633f3e3d88d06bd699c/mistune-3.0.2-py3-none-any.whl
  sha256: 71481854c30fdbc938963d3605b72501f5c10a9320ecd412c121c163a1c7d205
  requires_python: '>=3.7'
- kind: conda
  name: mkl
  version: 2023.0.0
  build: h84fe81f_26648
  build_number: 26648
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/mkl-2023.0.0-h84fe81f_26648.conda
  sha256: 989c614ec8417a22f61e9d4ec920c88910bbc3239885a6f20ad4eacad09f277c
  md5: 2a37425a272410241c7613c38e3955b2
  depends:
  - _openmp_mutex * *_llvm
  - _openmp_mutex >=4.5
  - llvm-openmp >=15.0.7
  - tbb 2021.*
  license: LicenseRef-ProprietaryIntel
  license_family: Proprietary
  size: 161440193
  timestamp: 1674673370494
- kind: conda
  name: mpc
  version: 1.3.1
  build: hfe3b2da_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/mpc-1.3.1-hfe3b2da_0.conda
  sha256: 2f88965949ba7b4b21e7e5facd62285f7c6efdb17359d1b365c3bb4ecc968d29
  md5: 289c71e83dc0daa7d4c81f04180778ca
  depends:
  - gmp >=6.2.1,<7.0a0
  - libgcc-ng >=12
  - mpfr >=4.1.0,<5.0a0
  license: LGPL-3.0-or-later
  license_family: LGPL
  size: 116276
  timestamp: 1674263855481
- kind: conda
  name: mpfr
  version: 4.2.1
  build: h9458935_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/mpfr-4.2.1-h9458935_1.conda
  sha256: 38c501f6b8dff124e57711c01da23e204703a3c14276f4cf6abd28850b2b9893
  md5: 8083b20f566639c22f78bcd6ca35b276
  depends:
  - gmp >=6.3.0,<7.0a0
  - libgcc-ng >=12
  license: LGPL-3.0-only
  license_family: LGPL
  size: 643060
  timestamp: 1712339500544
- kind: conda
  name: mpg123
  version: 1.32.6
  build: h59595ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/mpg123-1.32.6-h59595ed_0.conda
  sha256: 8895a5ce5122a3b8f59afcba4b032f198e8a690a0efc95ef61f2135357ef0d72
  md5: 9160cdeb523a1b20cf8d2a0bf821f45d
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: LGPL-2.1-only
  license_family: LGPL
  size: 491811
  timestamp: 1712327176955
- kind: conda
  name: mpmath
  version: 1.3.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/mpmath-1.3.0-pyhd8ed1ab_0.conda
  sha256: a4f025c712ec1502a55c471b56a640eaeebfce38dd497d5a1a33729014cac47a
  md5: dbf6e2d89137da32fa6670f3bffc024e
  depends:
  - python >=3.6
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/mpmath
  size: 438339
  timestamp: 1678228210181
- kind: pypi
  name: msgpack
  version: 1.0.8
  url: https://files.pythonhosted.org/packages/d9/96/a1868dd8997d65732476dfc70fef44d046c1b4dbe36ec1481ab744d87775/msgpack-1.0.8-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: 00e073efcba9ea99db5acef3959efa45b52bc67b61b00823d2a1a6944bf45982
  requires_python: '>=3.8'
- kind: pypi
  name: msgpack-numpy
  version: 0.4.8
  url: https://files.pythonhosted.org/packages/9b/5d/f25ac7d4fb77cbd53ddc6d05d833c6bf52b12770a44fa9a447eed470ca9a/msgpack_numpy-0.4.8-py2.py3-none-any.whl
  sha256: 773c19d4dfbae1b3c7b791083e2caf66983bb19b40901646f61d8731554ae3da
  requires_dist:
  - numpy>=1.9.0
  - msgpack>=0.5.2
- kind: pypi
  name: msgspec
  version: 0.19.0
  url: https://files.pythonhosted.org/packages/c6/65/080509c5774a1592b2779d902a70b5fe008532759927e011f068145a16cb/msgspec-0.19.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: 00e87ecfa9795ee5214861eab8326b0e75475c2e68a384002aa135ea2a27d909
  requires_dist:
  - pyyaml ; extra == 'yaml'
  - tomli ; python_version < '3.11' and extra == 'toml'
  - tomli-w ; extra == 'toml'
  - sphinx ; extra == 'doc'
  - furo ; extra == 'doc'
  - sphinx-copybutton ; extra == 'doc'
  - sphinx-design ; extra == 'doc'
  - ipython ; extra == 'doc'
  - pytest ; extra == 'test'
  - msgpack ; extra == 'test'
  - attrs ; extra == 'test'
  - eval-type-backport ; python_version < '3.10' and extra == 'test'
  - pyyaml ; extra == 'test'
  - tomli ; python_version < '3.11' and extra == 'test'
  - tomli-w ; extra == 'test'
  - pre-commit ; extra == 'dev'
  - coverage ; extra == 'dev'
  - mypy ; extra == 'dev'
  - pyright ; extra == 'dev'
  - sphinx ; extra == 'dev'
  - furo ; extra == 'dev'
  - sphinx-copybutton ; extra == 'dev'
  - sphinx-design ; extra == 'dev'
  - ipython ; extra == 'dev'
  - pytest ; extra == 'dev'
  - msgpack ; extra == 'dev'
  - attrs ; extra == 'dev'
  - eval-type-backport ; python_version < '3.10' and extra == 'dev'
  - pyyaml ; extra == 'dev'
  - tomli ; python_version < '3.11' and extra == 'dev'
  - tomli-w ; extra == 'dev'
  requires_python: '>=3.9'
- kind: pypi
  name: multiprocess
  version: 0.70.16
  url: https://files.pythonhosted.org/packages/bc/f7/7ec7fddc92e50714ea3745631f79bd9c96424cb2702632521028e57d3a36/multiprocess-0.70.16-py310-none-any.whl
  sha256: c4a9944c67bd49f823687463660a2d6daae94c289adff97e0f9d696ba6371d02
  requires_dist:
  - dill>=0.3.8
  requires_python: '>=3.8'
- kind: conda
  name: mysql-common
  version: 8.3.0
  build: hf1915f5_4
  build_number: 4
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/mysql-common-8.3.0-hf1915f5_4.conda
  sha256: 4cf6d29e091398735348550cb74cfd5006e04892d54b6b1ba916935f1af1a151
  md5: 784a4df6676c581ca624fbe460703a6d
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - openssl >=3.2.1,<4.0a0
  license: GPL-2.0-or-later
  license_family: GPL
  size: 784844
  timestamp: 1709910607121
- kind: conda
  name: mysql-libs
  version: 8.3.0
  build: hca2cd23_4
  build_number: 4
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/mysql-libs-8.3.0-hca2cd23_4.conda
  sha256: c39cdd1a5829aeffc611f789bdfd4dbd4ce1aa829c73d728defec180b5265d91
  md5: 1b50eebe2a738a3146c154d2eceaa8b6
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - mysql-common 8.3.0 hf1915f5_4
  - openssl >=3.2.1,<4.0a0
  - zstd >=1.5.5,<1.6.0a0
  license: GPL-2.0-or-later
  license_family: GPL
  size: 1537884
  timestamp: 1709910705541
- kind: pypi
  name: nbclient
  version: 0.10.0
  url: https://files.pythonhosted.org/packages/66/e8/00517a23d3eeaed0513e718fbc94aab26eaa1758f5690fc8578839791c79/nbclient-0.10.0-py3-none-any.whl
  sha256: f13e3529332a1f1f81d82a53210322476a168bb7090a0289c795fe9cc11c9d3f
  requires_dist:
  - jupyter-client>=6.1.12
  - jupyter-core!=5.0.*,>=4.12
  - nbformat>=5.1
  - traitlets>=5.4
  - pre-commit ; extra == 'dev'
  - autodoc-traits ; extra == 'docs'
  - mock ; extra == 'docs'
  - moto ; extra == 'docs'
  - myst-parser ; extra == 'docs'
  - nbclient[test] ; extra == 'docs'
  - sphinx-book-theme ; extra == 'docs'
  - sphinx>=1.7 ; extra == 'docs'
  - sphinxcontrib-spelling ; extra == 'docs'
  - flaky ; extra == 'test'
  - ipykernel>=6.19.3 ; extra == 'test'
  - ipython ; extra == 'test'
  - ipywidgets ; extra == 'test'
  - nbconvert>=7.0.0 ; extra == 'test'
  - pytest-asyncio ; extra == 'test'
  - pytest-cov>=4.0 ; extra == 'test'
  - pytest<8,>=7.0 ; extra == 'test'
  - testpath ; extra == 'test'
  - xmltodict ; extra == 'test'
  requires_python: '>=3.8.0'
- kind: pypi
  name: nbconvert
  version: 7.16.4
  url: https://files.pythonhosted.org/packages/b8/bb/bb5b6a515d1584aa2fd89965b11db6632e4bdc69495a52374bcc36e56cfa/nbconvert-7.16.4-py3-none-any.whl
  sha256: 05873c620fe520b6322bf8a5ad562692343fe3452abda5765c7a34b7d1aa3eb3
  requires_dist:
  - beautifulsoup4
  - bleach!=5.0.0
  - defusedxml
  - importlib-metadata>=3.6 ; python_version < '3.10'
  - jinja2>=3.0
  - jupyter-core>=4.7
  - jupyterlab-pygments
  - markupsafe>=2.0
  - mistune<4,>=2.0.3
  - nbclient>=0.5.0
  - nbformat>=5.7
  - packaging
  - pandocfilters>=1.4.1
  - pygments>=2.4.1
  - tinycss2
  - traitlets>=5.1
  - flaky ; extra == 'all'
  - ipykernel ; extra == 'all'
  - ipython ; extra == 'all'
  - ipywidgets>=7.5 ; extra == 'all'
  - myst-parser ; extra == 'all'
  - nbsphinx>=0.2.12 ; extra == 'all'
  - playwright ; extra == 'all'
  - pydata-sphinx-theme ; extra == 'all'
  - pyqtwebengine>=5.15 ; extra == 'all'
  - pytest>=7 ; extra == 'all'
  - sphinx==5.0.2 ; extra == 'all'
  - sphinxcontrib-spelling ; extra == 'all'
  - tornado>=6.1 ; extra == 'all'
  - ipykernel ; extra == 'docs'
  - ipython ; extra == 'docs'
  - myst-parser ; extra == 'docs'
  - nbsphinx>=0.2.12 ; extra == 'docs'
  - pydata-sphinx-theme ; extra == 'docs'
  - sphinx==5.0.2 ; extra == 'docs'
  - sphinxcontrib-spelling ; extra == 'docs'
  - pyqtwebengine>=5.15 ; extra == 'qtpdf'
  - pyqtwebengine>=5.15 ; extra == 'qtpng'
  - tornado>=6.1 ; extra == 'serve'
  - flaky ; extra == 'test'
  - ipykernel ; extra == 'test'
  - ipywidgets>=7.5 ; extra == 'test'
  - pytest>=7 ; extra == 'test'
  - playwright ; extra == 'webpdf'
  requires_python: '>=3.8'
- kind: pypi
  name: nbformat
  version: 5.10.4
  url: https://files.pythonhosted.org/packages/a9/82/0340caa499416c78e5d8f5f05947ae4bc3cba53c9f038ab6e9ed964e22f1/nbformat-5.10.4-py3-none-any.whl
  sha256: 3b48d6c8fbca4b299bf3982ea7db1af21580e4fec269ad087b9e81588891200b
  requires_dist:
  - fastjsonschema>=2.15
  - jsonschema>=2.6
  - jupyter-core!=5.0.*,>=4.12
  - traitlets>=5.1
  - myst-parser ; extra == 'docs'
  - pydata-sphinx-theme ; extra == 'docs'
  - sphinx ; extra == 'docs'
  - sphinxcontrib-github-alt ; extra == 'docs'
  - sphinxcontrib-spelling ; extra == 'docs'
  - pep440 ; extra == 'test'
  - pre-commit ; extra == 'test'
  - pytest ; extra == 'test'
  - testpath ; extra == 'test'
  requires_python: '>=3.8'
- kind: conda
  name: ncurses
  version: 6.4.20240210
  build: h59595ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.4.20240210-h59595ed_0.conda
  sha256: aa0f005b6727aac6507317ed490f0904430584fa8ca722657e7f0fb94741de81
  md5: 97da8860a0da5413c7c98a3b3838a645
  depends:
  - libgcc-ng >=12
  license: X11 AND BSD-3-Clause
  size: 895669
  timestamp: 1710866638986
- kind: pypi
  name: nerfacc
  version: 0.5.2
  url: https://files.pythonhosted.org/packages/f4/16/e93ebb619a3edd8851df21425fe20b7491bd1d819fc19beb9bfc1bf2556e/nerfacc-0.5.2-py3-none-any.whl
  sha256: fbc5afb135c9397fafa45132c0615dab8b60be40979911a8a4e829628815bdb7
  requires_dist:
  - rich>=12
  - torch
  - typing-extensions ; python_version < '3.8'
  - black[jupyter]==22.3.0 ; extra == 'dev'
  - isort==5.10.1 ; extra == 'dev'
  - pylint==2.13.4 ; extra == 'dev'
  - pytest==7.1.2 ; extra == 'dev'
  - pytest-xdist==2.5.0 ; extra == 'dev'
  - typeguard>=2.13.3 ; extra == 'dev'
  - pyyaml==6.0 ; extra == 'dev'
  - build ; extra == 'dev'
  - twine ; extra == 'dev'
  requires_python: '>=3.7'
- kind: pypi
  name: nerfstudio
  version: 1.1.5
  path: .
  sha256: add4015bb2a80ce09a419266951dc287ef72c226c267ec0a28a11b16da3b09d8
  requires_dist:
  - appdirs>=1.4
  - av>=9.2.0
  - comet-ml>=3.33.8
  - cryptography>=38
  - tyro>=0.9.8
  - gdown>=4.6.0
  - ninja>=1.10
  - h5py>=2.9.0
  - imageio>=2.21.1
  - importlib-metadata>=6.0.0 ; python_version < '3.10'
  - ipywidgets>=7.6
  - jaxtyping>=0.2.15
  - jupyterlab>=3.3.4
  - matplotlib>=3.6.0
  - mediapy>=1.1.0
  - msgpack>=1.0.4
  - msgpack-numpy>=0.4.8
  - nerfacc==0.5.2
  - open3d>=0.16.0
  - opencv-python-headless==*********
  - pillow>=10.3.0
  - plotly>=5.7.0
  - protobuf<=3.20.3,!=3.20.0
  - pymeshlab>=2022.2.post2 ; platform_machine != 'arm64' and platform_machine != 'aarch64'
  - pymeshlab<2023.12.post2 ; sys_platform == 'win32' and platform_machine != 'arm64' and platform_machine != 'aarch64'
  - pyngrok>=5.1.0
  - python-socketio>=5.7.1
  - pyquaternion>=0.9.9
  - rawpy>=0.18.1 ; platform_machine != 'arm64'
  - newrawpy>=1.0.0b0 ; platform_machine == 'arm64'
  - requests
  - rich>=12.5.1
  - scikit-image>=0.19.3
  - splines==0.3.0
  - tensorboard>=2.13.0
  - torch>=1.13.1
  - torchvision>=0.14.1
  - torchmetrics[image]>=1.0.1
  - typing-extensions>=4.4.0
  - viser==0.2.7
  - nuscenes-devkit>=1.1.1
  - wandb>=0.13.3
  - xatlas
  - trimesh>=3.20.2
  - timm==0.6.7
  - gsplat==1.4.0
  - pytorch-msssim
  - pathos
  - packaging
  - fpsample
  - tensorly
  - diffusers==0.16.1 ; extra == 'gen'
  - transformers==4.29.2 ; extra == 'gen'
  - accelerate==0.19.0 ; extra == 'gen'
  - bitsandbytes==0.39.0 ; extra == 'gen'
  - sentencepiece==0.1.99 ; extra == 'gen'
  - pre-commit==3.3.2 ; extra == 'dev'
  - pytest==7.1.2 ; extra == 'dev'
  - pytest-xdist==2.5.0 ; extra == 'dev'
  - ruff>=0.6.1 ; extra == 'dev'
  - sshconf==0.2.5 ; extra == 'dev'
  - pycolmap>=0.3.0 ; extra == 'dev'
  - diffusers==0.16.1 ; extra == 'dev'
  - opencv-stubs==0.0.7 ; extra == 'dev'
  - transformers==4.29.2 ; extra == 'dev'
  - pyright==1.1.331 ; extra == 'dev'
  - projectaria-tools>=1.3.1 ; sys_platform != 'win32' and extra == 'dev'
  - torch>=1.13.1,<2.2 ; extra == 'dev'
  - awscli==1.33.18 ; extra == 'dev'
  - furo==2022.9.29 ; extra == 'docs'
  - ipython==8.6.0 ; extra == 'docs'
  - readthedocs-sphinx-search==0.1.2 ; extra == 'docs'
  - myst-nb==0.16.0 ; extra == 'docs'
  - nbconvert==7.2.5 ; extra == 'docs'
  - nbformat==5.9.2 ; extra == 'docs'
  - sphinx==5.2.1 ; extra == 'docs'
  - sphinxemoji==0.2.0 ; extra == 'docs'
  - sphinx-argparse==0.3.1 ; extra == 'docs'
  - sphinx-copybutton==0.5.0 ; extra == 'docs'
  - sphinx-design==0.2.0 ; extra == 'docs'
  - sphinxext-opengraph==0.6.3 ; extra == 'docs'
  requires_python: '>=3.8.0'
  editable: true
- kind: pypi
  name: nest-asyncio
  version: 1.6.0
  url: https://files.pythonhosted.org/packages/a0/c4/c2971a3ba4c6103a3d10c4b0f24f461ddc027f0f09763220cf35ca1401b3/nest_asyncio-1.6.0-py3-none-any.whl
  sha256: 87af6efd6b5e897c81050477ef65c62e2b2f35d51703cae01aff2905b1852e1c
  requires_python: '>=3.5'
- kind: conda
  name: nettle
  version: 3.9.1
  build: h7ab15ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/nettle-3.9.1-h7ab15ed_0.conda
  sha256: 1ef1b7efa69c7fb4e2a36a88316f307c115713698d1c12e19f55ae57c0482995
  md5: 2bf1915cc107738811368afcb0993a59
  depends:
  - libgcc-ng >=12
  license: GPL 2 and LGPL3
  license_family: GPL
  size: 1011638
  timestamp: 1686309814836
- kind: conda
  name: networkx
  version: '3.3'
  build: pyhd8ed1ab_1
  build_number: 1
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/networkx-3.3-pyhd8ed1ab_1.conda
  sha256: cbd8a6de87ad842e7665df38dcec719873fe74698bc761de5431047b8fada41a
  md5: d335fd5704b46f4efb89a6774e81aef0
  depends:
  - python >=3.10
  constrains:
  - pandas >=1.4
  - numpy >=1.22
  - matplotlib >=3.5
  - scipy >=1.9,!=1.11.0,!=1.11.1
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/networkx
  size: 1185670
  timestamp: 1712540499262
- kind: pypi
  name: ninja
  version: ********
  url: https://files.pythonhosted.org/packages/6d/92/8d7aebd4430ab5ff65df2bfee6d5745f95c004284db2d8ca76dcbfd9de47/ninja-********-py2.py3-none-manylinux1_x86_64.manylinux_2_5_x86_64.whl
  sha256: 84502ec98f02a037a169c4b0d5d86075eaf6afc55e1879003d6cab51ced2ea4b
  requires_dist:
  - codecov>=2.0.5 ; extra == 'test'
  - coverage>=4.2 ; extra == 'test'
  - flake8>=3.0.4 ; extra == 'test'
  - pytest>=4.5.0 ; extra == 'test'
  - pytest-cov>=2.7.1 ; extra == 'test'
  - pytest-runner>=5.1 ; extra == 'test'
  - pytest-virtualenv>=1.7.0 ; extra == 'test'
  - virtualenv>=15.0.3 ; extra == 'test'
- kind: pypi
  name: nodeenv
  version: 1.8.0
  url: https://files.pythonhosted.org/packages/1a/e6/6d2ead760a9ddb35e65740fd5a57e46aadd7b0c49861ab24f94812797a1c/nodeenv-1.8.0-py2.py3-none-any.whl
  sha256: df865724bb3c3adc86b3876fa209771517b0cfe596beff01a92700e0e8be4cec
  requires_dist:
  - setuptools
  requires_python: '>=2.7,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*,!=3.5.*,!=3.6.*'
- kind: pypi
  name: notebook
  version: 7.0.7
  url: https://files.pythonhosted.org/packages/f2/57/2f8d59ddc7f2d0d8ac4f80f869545bc44646fc78c1c083b3655c58e3edfb/notebook-7.0.7-py3-none-any.whl
  sha256: 289b606d7e173f75a18beb1406ef411b43f97f7a9c55ba03efa3622905a62346
  requires_dist:
  - jupyter-server<3,>=2.4.0
  - jupyterlab-server<3,>=2.22.1
  - jupyterlab<5,>=4.0.2
  - notebook-shim<0.3,>=0.2
  - tornado>=6.2.0
  - hatch ; extra == 'dev'
  - pre-commit ; extra == 'dev'
  - myst-parser ; extra == 'docs'
  - nbsphinx ; extra == 'docs'
  - pydata-sphinx-theme ; extra == 'docs'
  - sphinx>=1.3.6 ; extra == 'docs'
  - sphinxcontrib-github-alt ; extra == 'docs'
  - sphinxcontrib-spelling ; extra == 'docs'
  - importlib-resources>=5.0 ; python_version < '3.10' and extra == 'test'
  - ipykernel ; extra == 'test'
  - jupyter-server[test]<3,>=2.4.0 ; extra == 'test'
  - jupyterlab-server[test]<3,>=2.22.1 ; extra == 'test'
  - nbval ; extra == 'test'
  - pytest-console-scripts ; extra == 'test'
  - pytest-timeout ; extra == 'test'
  - pytest-tornasync ; extra == 'test'
  - pytest>=7.0 ; extra == 'test'
  - requests ; extra == 'test'
  requires_python: '>=3.8'
- kind: pypi
  name: notebook-shim
  version: 0.2.4
  url: https://files.pythonhosted.org/packages/f9/33/bd5b9137445ea4b680023eb0469b2bb969d61303dedb2aac6560ff3d14a1/notebook_shim-0.2.4-py3-none-any.whl
  sha256: 411a5be4e9dc882a074ccbcae671eda64cceb068767e9a3419096986560e1cef
  requires_dist:
  - jupyter-server<3,>=1.8
  - pytest ; extra == 'test'
  - pytest-console-scripts ; extra == 'test'
  - pytest-jupyter ; extra == 'test'
  - pytest-tornasync ; extra == 'test'
  requires_python: '>=3.7'
- kind: conda
  name: nsight-compute
  version: 2022.3.0.22
  build: '0'
  subdir: linux-64
  url: https://conda.anaconda.org/nvidia/label/cuda-11.8.0/linux-64/nsight-compute-2022.3.0.22-0.tar.bz2
  md5: a438d9120c100bcac9eb166cebdd3023
  arch: x86_64
  platform: linux
  size: 639598244
  timestamp: 1661399409484
- kind: conda
  name: nspr
  version: '4.35'
  build: h27087fc_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/nspr-4.35-h27087fc_0.conda
  sha256: 8fadeebb2b7369a4f3b2c039a980d419f65c7b18267ba0c62588f9f894396d0c
  md5: da0ec11a6454ae19bff5b02ed881a2b1
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MPL-2.0
  license_family: MOZILLA
  size: 226848
  timestamp: 1669784948267
- kind: conda
  name: nss
  version: '3.98'
  build: h1d7d5a4_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/nss-3.98-h1d7d5a4_0.conda
  sha256: a9bc94d03df48014011cf6caaf447f2ef86a5edf7c70d70002ec4b59f5a4e198
  md5: 54b56c2fdf973656b748e0378900ec13
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libsqlite >=3.45.1,<4.0a0
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - nspr >=4.35,<5.0a0
  license: MPL-2.0
  license_family: MOZILLA
  size: 2019716
  timestamp: 1708065114928
- kind: conda
  name: numpy
  version: 1.26.4
  build: py310hb13e2d6_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/numpy-1.26.4-py310hb13e2d6_0.conda
  sha256: 028fe2ea8e915a0a032b75165f11747770326f3d767e642880540c60a3256425
  md5: 6593de64c935768b6bad3e19b3e978be
  depends:
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libgcc-ng >=12
  - liblapack >=3.9.0,<4.0a0
  - libstdcxx-ng >=12
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/numpy
  size: 7009070
  timestamp: 1707225917496
- kind: pypi
  name: nuscenes-devkit
  version: 1.1.9
  url: https://files.pythonhosted.org/packages/c6/53/460bf754677b3b247fb99a447e3575490dbc5f42ec94d528bc0137176f6a/nuscenes_devkit-1.1.9-py3-none-any.whl
  sha256: 8a818aaa8566e06960a57d1f88073f5079187bb056dcdab4d6fb54afd63a558c
  requires_dist:
  - cachetools
  - descartes
  - fire
  - jupyter
  - matplotlib
  - numpy
  - opencv-python
  - pillow>6.2.1
  - pyquaternion>=0.9.5
  - scikit-learn
  - scipy
  - shapely
  - tqdm
  - pycocotools>=2.0.1
  requires_python: '>=3.6'
- kind: conda
  name: ocl-icd
  version: 2.3.2
  build: hd590300_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/ocl-icd-2.3.2-hd590300_1.conda
  sha256: 0e01384423e48e5011eb6b224da8dc5e3567c87dbcefbe60cd9d5cead276cdcd
  md5: c66f837ac65e4d1cdeb80e2a1d5fcc3d
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 135681
  timestamp: 1710946531879
- kind: pypi
  name: open3d
  version: 0.18.0
  url: https://files.pythonhosted.org/packages/3b/e1/fc609763d982c6c43ee9503279fa6dc42d048b6224a9dc0a0ce8ac19308a/open3d-0.18.0-cp310-cp310-manylinux_2_27_x86_64.whl
  sha256: f649d5d58090f73a337895fb0022c7b05c00f47f704b5722b103cceba04cc870
  requires_dist:
  - numpy>=1.18.0
  - dash>=2.6.0
  - werkzeug>=2.2.3
  - nbformat>=5.7.0
  - configargparse
  - ipywidgets>=8.0.4
  - addict
  - pillow>=9.3.0
  - matplotlib>=3
  - numpy>1.18
  - pandas>=1.0
  - pyyaml>=5.4.1
  - scikit-learn>=0.21
  - tqdm
  - pyquaternion
  - pywinpty==2.0.2 ; sys_platform == 'win32' and python_version == '3.6'
  requires_python: '>=3.8'
- kind: pypi
  name: opencv-python
  version: ********
  url: https://files.pythonhosted.org/packages/f5/d0/2e455d894ec0d6527e662ad55e70c04f421ad83a6fd0a54c3dd73c411282/opencv_python-********-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: 9bcb4944211acf13742dbfd9d3a11dc4e36353ffa1746f2c7dcd6a01c32d1376
  requires_dist:
  - numpy>=1.13.3 ; python_version < '3.7'
  - numpy>=1.21.0 ; python_version <= '3.9' and platform_system == 'Darwin' and platform_machine == 'arm64'
  - numpy>=1.21.2 ; python_version >= '3.10'
  - numpy>=1.21.4 ; python_version >= '3.10' and platform_system == 'Darwin'
  - numpy>=1.23.5 ; python_version >= '3.11'
  - numpy>=1.19.3 ; python_version >= '3.6' and platform_system == 'Linux' and platform_machine == 'aarch64'
  - numpy>=1.17.0 ; python_version >= '3.7'
  - numpy>=1.17.3 ; python_version >= '3.8'
  - numpy>=1.19.3 ; python_version >= '3.9'
  requires_python: '>=3.6'
- kind: pypi
  name: opencv-python-headless
  version: *********
  url: https://files.pythonhosted.org/packages/d1/09/248f86a404567303cdf120e4a301f389b68e3b18e5c0cc428de327da609c/opencv_python_headless-*********-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: 377d08a7e48a1405b5e84afcbe4798464ce7ee17081c1c23619c8b398ff18295
  requires_dist:
  - numpy>=1.13.3 ; python_version < '3.7'
  - numpy>=1.21.0 ; python_version <= '3.9' and platform_system == 'Darwin' and platform_machine == 'arm64'
  - numpy>=1.21.2 ; python_version >= '3.10'
  - numpy>=1.21.4 ; python_version >= '3.10' and platform_system == 'Darwin'
  - numpy>=1.23.5 ; python_version >= '3.11'
  - numpy>=1.26.0 ; python_version >= '3.12'
  - numpy>=1.19.3 ; python_version >= '3.6' and platform_system == 'Linux' and platform_machine == 'aarch64'
  - numpy>=1.17.0 ; python_version >= '3.7'
  - numpy>=1.17.3 ; python_version >= '3.8'
  - numpy>=1.19.3 ; python_version >= '3.9'
  requires_python: '>=3.6'
- kind: conda
  name: openexr
  version: 3.2.2
  build: haf962dd_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/openexr-3.2.2-haf962dd_1.conda
  sha256: 01d773a14124929abd6c26169d900ce439f9df8a9e37d3ea197c7f71f61e7906
  md5: 34e58e21fc28e404207d6ce4287da264
  depends:
  - imath >=3.1.11,<3.1.12.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 1466865
  timestamp: 1709260550301
- kind: conda
  name: openh264
  version: 2.4.1
  build: h59595ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/openh264-2.4.1-h59595ed_0.conda
  sha256: 0d4eaf15fb771f25c924aef831d76eea11d90c824778fc1e7666346e93475f42
  md5: 3dfcf61b8e78af08110f5229f79580af
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 735244
  timestamp: 1706873814072
- kind: conda
  name: openjpeg
  version: 2.5.2
  build: h488ebb8_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/openjpeg-2.5.2-h488ebb8_0.conda
  sha256: 5600a0b82df042bd27d01e4e687187411561dfc11cc05143a08ce29b64bf2af2
  md5: 7f2e286780f072ed750df46dc2631138
  depends:
  - libgcc-ng >=12
  - libpng >=1.6.43,<1.7.0a0
  - libstdcxx-ng >=12
  - libtiff >=4.6.0,<4.7.0a0
  - libzlib >=1.2.13,<1.3.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 341592
  timestamp: 1709159244431
- kind: conda
  name: openssl
  version: 3.3.0
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.3.0-hd590300_0.conda
  sha256: fdbf05e4db88c592366c90bb82e446edbe33c6e49e5130d51c580b2629c0b5d5
  md5: c0f3abb4a16477208bbd43a39bd56f18
  depends:
  - ca-certificates
  - libgcc-ng >=12
  constrains:
  - pyopenssl >=22.1
  license: Apache-2.0
  license_family: Apache
  size: 2895187
  timestamp: 1714466138265
- kind: conda
  name: orc
  version: 2.0.0
  build: h17fec99_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/orc-2.0.0-h17fec99_1.conda
  sha256: ccbfb6c2a01259c2c95b5b8139a0c3a8d4ec6240228ad1ac454b41f5fbcfd082
  md5: d2e0ffa6c3452f0a723a0ef1b96fd1cb
  depends:
  - libgcc-ng >=12
  - libprotobuf >=4.25.3,<4.25.4.0a0
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - lz4-c >=1.9.3,<1.10.0a0
  - snappy >=1.2.0,<1.3.0a0
  - zstd >=1.5.5,<1.6.0a0
  license: Apache-2.0
  license_family: Apache
  size: 1029252
  timestamp: 1712616110941
- kind: pypi
  name: overrides
  version: 7.7.0
  url: https://files.pythonhosted.org/packages/2c/ab/fc8290c6a4c722e5514d80f62b2dc4c4df1a68a41d1364e625c35990fcf3/overrides-7.7.0-py3-none-any.whl
  sha256: c7ed9d062f78b8e4c1a7b70bd8796b35ead4d9f510227ef9c5dc7626c60d7e49
  requires_dist:
  - typing ; python_version < '3.5'
  requires_python: '>=3.6'
- kind: conda
  name: p11-kit
  version: 0.24.1
  build: hc5aa10d_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/p11-kit-0.24.1-hc5aa10d_0.tar.bz2
  sha256: aa8d3887b36557ad0c839e4876c0496e0d670afe843bf5bba4a87764b868196d
  md5: 56ee94e34b71742bbdfa832c974e47a8
  depends:
  - libffi >=3.4.2,<3.5.0a0
  - libgcc-ng >=12
  - libtasn1 >=4.18.0,<5.0a0
  license: MIT
  license_family: MIT
  size: 4702497
  timestamp: 1654868759643
- kind: pypi
  name: packaging
  version: '24.0'
  url: https://files.pythonhosted.org/packages/49/df/1fceb2f8900f8639e278b056416d49134fb8d84c5942ffaa01ad34782422/packaging-24.0-py3-none-any.whl
  sha256: 2ddfb553fdf02fb784c234c7ba6ccc288296ceabec964ad2eae3777778130bc5
  requires_python: '>=3.7'
- kind: pypi
  name: pandas
  version: 2.2.2
  url: https://files.pythonhosted.org/packages/89/1b/12521efcbc6058e2673583bb096c2b5046a9df39bd73eca392c1efed24e5/pandas-2.2.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: 8635c16bf3d99040fdf3ca3db669a7250ddf49c55dc4aa8fe0ae0fa8d6dcc1f0
  requires_dist:
  - numpy>=1.22.4 ; python_version < '3.11'
  - numpy>=1.23.2 ; python_version == '3.11'
  - numpy>=1.26.0 ; python_version >= '3.12'
  - python-dateutil>=2.8.2
  - pytz>=2020.1
  - tzdata>=2022.7
  - hypothesis>=6.46.1 ; extra == 'test'
  - pytest>=7.3.2 ; extra == 'test'
  - pytest-xdist>=2.2.0 ; extra == 'test'
  - pyarrow>=10.0.1 ; extra == 'pyarrow'
  - bottleneck>=1.3.6 ; extra == 'performance'
  - numba>=0.56.4 ; extra == 'performance'
  - numexpr>=2.8.4 ; extra == 'performance'
  - scipy>=1.10.0 ; extra == 'computation'
  - xarray>=2022.12.0 ; extra == 'computation'
  - fsspec>=2022.11.0 ; extra == 'fss'
  - s3fs>=2022.11.0 ; extra == 'aws'
  - gcsfs>=2022.11.0 ; extra == 'gcp'
  - pandas-gbq>=0.19.0 ; extra == 'gcp'
  - odfpy>=1.4.1 ; extra == 'excel'
  - openpyxl>=3.1.0 ; extra == 'excel'
  - python-calamine>=0.1.7 ; extra == 'excel'
  - pyxlsb>=1.0.10 ; extra == 'excel'
  - xlrd>=2.0.1 ; extra == 'excel'
  - xlsxwriter>=3.0.5 ; extra == 'excel'
  - pyarrow>=10.0.1 ; extra == 'parquet'
  - pyarrow>=10.0.1 ; extra == 'feather'
  - tables>=3.8.0 ; extra == 'hdf5'
  - pyreadstat>=1.2.0 ; extra == 'spss'
  - sqlalchemy>=2.0.0 ; extra == 'postgresql'
  - psycopg2>=2.9.6 ; extra == 'postgresql'
  - adbc-driver-postgresql>=0.8.0 ; extra == 'postgresql'
  - sqlalchemy>=2.0.0 ; extra == 'mysql'
  - pymysql>=1.0.2 ; extra == 'mysql'
  - sqlalchemy>=2.0.0 ; extra == 'sql-other'
  - adbc-driver-postgresql>=0.8.0 ; extra == 'sql-other'
  - adbc-driver-sqlite>=0.8.0 ; extra == 'sql-other'
  - beautifulsoup4>=4.11.2 ; extra == 'html'
  - html5lib>=1.1 ; extra == 'html'
  - lxml>=4.9.2 ; extra == 'html'
  - lxml>=4.9.2 ; extra == 'xml'
  - matplotlib>=3.6.3 ; extra == 'plot'
  - jinja2>=3.1.2 ; extra == 'output-formatting'
  - tabulate>=0.9.0 ; extra == 'output-formatting'
  - pyqt5>=5.15.9 ; extra == 'clipboard'
  - qtpy>=2.3.0 ; extra == 'clipboard'
  - zstandard>=0.19.0 ; extra == 'compression'
  - dataframe-api-compat>=0.1.7 ; extra == 'consortium-standard'
  - adbc-driver-postgresql>=0.8.0 ; extra == 'all'
  - adbc-driver-sqlite>=0.8.0 ; extra == 'all'
  - beautifulsoup4>=4.11.2 ; extra == 'all'
  - bottleneck>=1.3.6 ; extra == 'all'
  - dataframe-api-compat>=0.1.7 ; extra == 'all'
  - fastparquet>=2022.12.0 ; extra == 'all'
  - fsspec>=2022.11.0 ; extra == 'all'
  - gcsfs>=2022.11.0 ; extra == 'all'
  - html5lib>=1.1 ; extra == 'all'
  - hypothesis>=6.46.1 ; extra == 'all'
  - jinja2>=3.1.2 ; extra == 'all'
  - lxml>=4.9.2 ; extra == 'all'
  - matplotlib>=3.6.3 ; extra == 'all'
  - numba>=0.56.4 ; extra == 'all'
  - numexpr>=2.8.4 ; extra == 'all'
  - odfpy>=1.4.1 ; extra == 'all'
  - openpyxl>=3.1.0 ; extra == 'all'
  - pandas-gbq>=0.19.0 ; extra == 'all'
  - psycopg2>=2.9.6 ; extra == 'all'
  - pyarrow>=10.0.1 ; extra == 'all'
  - pymysql>=1.0.2 ; extra == 'all'
  - pyqt5>=5.15.9 ; extra == 'all'
  - pyreadstat>=1.2.0 ; extra == 'all'
  - pytest>=7.3.2 ; extra == 'all'
  - pytest-xdist>=2.2.0 ; extra == 'all'
  - python-calamine>=0.1.7 ; extra == 'all'
  - pyxlsb>=1.0.10 ; extra == 'all'
  - qtpy>=2.3.0 ; extra == 'all'
  - scipy>=1.10.0 ; extra == 'all'
  - s3fs>=2022.11.0 ; extra == 'all'
  - sqlalchemy>=2.0.0 ; extra == 'all'
  - tables>=3.8.0 ; extra == 'all'
  - tabulate>=0.9.0 ; extra == 'all'
  - xarray>=2022.12.0 ; extra == 'all'
  - xlrd>=2.0.1 ; extra == 'all'
  - xlsxwriter>=3.0.5 ; extra == 'all'
  - zstandard>=0.19.0 ; extra == 'all'
  requires_python: '>=3.9'
- kind: pypi
  name: pandocfilters
  version: 1.5.1
  url: https://files.pythonhosted.org/packages/ef/af/4fbc8cab944db5d21b7e2a5b8e9211a03a79852b1157e2c102fcc61ac440/pandocfilters-1.5.1-py2.py3-none-any.whl
  sha256: 93be382804a9cdb0a7267585f157e5d1731bbe5545a85b268d6f5fe6232de2bc
  requires_python: '>=2.7,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*'
- kind: pypi
  name: parso
  version: 0.8.4
  url: https://files.pythonhosted.org/packages/c6/ac/dac4a63f978e4dcb3c6d3a78c4d8e0192a113d288502a1216950c41b1027/parso-0.8.4-py2.py3-none-any.whl
  sha256: a418670a20291dacd2dddc80c377c5c3791378ee1e8d12bffc35420643d43f18
  requires_dist:
  - flake8==5.0.4 ; extra == 'qa'
  - mypy==0.971 ; extra == 'qa'
  - types-setuptools==******** ; extra == 'qa'
  - docopt ; extra == 'testing'
  - pytest ; extra == 'testing'
  requires_python: '>=3.6'
- kind: pypi
  name: pathos
  version: 0.3.2
  url: https://files.pythonhosted.org/packages/f4/7f/cea34872c000d17972dad998575d14656d7c6bcf1a08a8d66d73c1ef2cca/pathos-0.3.2-py3-none-any.whl
  sha256: d669275e6eb4b3fbcd2846d7a6d1bba315fe23add0c614445ba1408d8b38bafe
  requires_dist:
  - ppft>=*******
  - dill>=0.3.8
  - pox>=0.3.4
  - multiprocess>=0.70.16
  requires_python: '>=3.8'
- kind: conda
  name: pcre2
  version: '10.43'
  build: hcad00b1_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.43-hcad00b1_0.conda
  sha256: 766dd986a7ed6197676c14699000bba2625fd26c8a890fcb7a810e5cf56155bc
  md5: 8292dea9e022d9610a11fce5e0896ed8
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - libgcc-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 950847
  timestamp: 1708118050286
- kind: pypi
  name: pexpect
  version: 4.9.0
  url: https://files.pythonhosted.org/packages/9e/c3/059298687310d527a58bb01f3b1965787ee3b40dce76752eda8b44e9a2c5/pexpect-4.9.0-py2.py3-none-any.whl
  sha256: 7236d1e080e4936be2dc3e326cec0af72acf9212a7e1d060210e70a47e253523
  requires_dist:
  - ptyprocess>=0.5
- kind: conda
  name: pillow
  version: 10.3.0
  build: py310hf73ecf8_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/pillow-10.3.0-py310hf73ecf8_0.conda
  sha256: 89caf2bb9b6d6d0c874590128b36676615750b5ef121fab514bc737dc48534da
  md5: 1de56cf017dfd02aa84093206a0141a8
  depends:
  - freetype >=2.12.1,<3.0a0
  - lcms2 >=2.16,<3.0a0
  - libgcc-ng >=12
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libtiff >=4.6.0,<4.7.0a0
  - libwebp-base >=1.3.2,<2.0a0
  - libxcb >=1.15,<1.16.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - openjpeg >=2.5.2,<3.0a0
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  - tk >=8.6.13,<8.7.0a0
  license: HPND
  purls:
  - pkg:pypi/pillow
  size: 41783273
  timestamp: 1712154626576
- kind: conda
  name: pip
  version: '24.0'
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/pip-24.0-pyhd8ed1ab_0.conda
  sha256: b7c1c5d8f13e8cb491c4bd1d0d1896a4cf80fc47de01059ad77509112b664a4a
  md5: f586ac1e56c8638b64f9c8122a7b8a67
  depends:
  - python >=3.7
  - setuptools
  - wheel
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pip
  size: 1398245
  timestamp: 1706960660581
- kind: conda
  name: pixman
  version: 0.43.2
  build: h59595ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/pixman-0.43.2-h59595ed_0.conda
  sha256: 366d28e2a0a191d6c535e234741e0cd1d94d713f76073d8af4a5ccb2a266121e
  md5: 71004cbf7924e19c02746ccde9fd7123
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  size: 386826
  timestamp: 1706549500138
- kind: pypi
  name: platformdirs
  version: 4.2.1
  url: https://files.pythonhosted.org/packages/b0/15/1691fa5aaddc0c4ea4901c26f6137c29d5f6673596fe960a0340e8c308e1/platformdirs-4.2.1-py3-none-any.whl
  sha256: 17d5a1161b3fd67b390023cb2d3b026bbd40abde6fdb052dfbd3a29c3ba22ee1
  requires_dist:
  - furo>=2023.9.10 ; extra == 'docs'
  - proselint>=0.13 ; extra == 'docs'
  - sphinx-autodoc-typehints>=1.25.2 ; extra == 'docs'
  - sphinx>=7.2.6 ; extra == 'docs'
  - appdirs==1.4.4 ; extra == 'test'
  - covdefaults>=2.3 ; extra == 'test'
  - pytest-cov>=4.1 ; extra == 'test'
  - pytest-mock>=3.12 ; extra == 'test'
  - pytest>=7.4.3 ; extra == 'test'
  - mypy>=1.8 ; extra == 'type'
  requires_python: '>=3.8'
- kind: pypi
  name: plotly
  version: 5.22.0
  url: https://files.pythonhosted.org/packages/0b/f8/b65cdd2be32e442c4efe7b672f73c90b05eab5a7f3f4115efe181d432c60/plotly-5.22.0-py3-none-any.whl
  sha256: 68fc1901f098daeb233cc3dd44ec9dc31fb3ca4f4e53189344199c43496ed006
  requires_dist:
  - tenacity>=6.2.0
  - packaging
  requires_python: '>=3.8'
- kind: pypi
  name: plyfile
  version: '1.1'
  url: https://files.pythonhosted.org/packages/49/ac/3d0ca39b304cbeee43a80ac8ea5ba85576373c00e30fb3e1c5d51bfa7e20/plyfile-1.1-py3-none-any.whl
  sha256: 897acde0bbe94af4f6c3866b2c0af2e889ae3533fd5c4f3d3318a0c421fcbe35
  requires_dist:
  - numpy>=1.21
  requires_python: '>=3.9'
- kind: pypi
  name: pox
  version: 0.3.4
  url: https://files.pythonhosted.org/packages/e1/d7/9e73c32f73da71e8224b4cb861b5db50ebdebcdff14d3e3fb47a63c578b2/pox-0.3.4-py3-none-any.whl
  sha256: 651b8ae8a7b341b7bfd267f67f63106daeb9805f1ac11f323d5280d2da93fdb6
  requires_python: '>=3.8'
- kind: pypi
  name: ppft
  version: *******
  url: https://files.pythonhosted.org/packages/ff/fa/5160c7d2fb1d4f2b83cba7a40f0eb4b015b78f6973b7ab6b2e73c233cfdc/ppft-*******-py3-none-any.whl
  sha256: de2dd4b1b080923dd9627fbdea52649fd741c752fce4f3cf37e26f785df23d9b
  requires_dist:
  - dill>=0.3.8 ; extra == 'dill'
  requires_python: '>=3.8'
- kind: pypi
  name: pretty-errors
  version: 1.2.25
  url: https://files.pythonhosted.org/packages/6d/8e/2df7467a15eae40e26c476683962fdb810cd1b36676603e2f139b4abbeaf/pretty_errors-1.2.25-py3-none-any.whl
  sha256: 8ce68ccd99e0f2a099265c8c1f1c23b7c60a15d69bb08816cb336e237d5dc983
  requires_dist:
  - colorama
- kind: pypi
  name: prometheus-client
  version: 0.20.0
  url: https://files.pythonhosted.org/packages/c7/98/745b810d822103adca2df8decd4c0bbe839ba7ad3511af3f0d09692fc0f0/prometheus_client-0.20.0-py3-none-any.whl
  sha256: cde524a85bce83ca359cc837f28b8c0db5cac7aa653a588fd7e84ba061c329e7
  requires_dist:
  - twisted ; extra == 'twisted'
  requires_python: '>=3.8'
- kind: pypi
  name: prompt-toolkit
  version: 3.0.43
  url: https://files.pythonhosted.org/packages/ee/fd/ca7bf3869e7caa7a037e23078539467b433a4e01eebd93f77180ab927766/prompt_toolkit-3.0.43-py3-none-any.whl
  sha256: a11a29cb3bf0a28a387fe5122cdb649816a957cd9261dcedf8c9f1fef33eacf6
  requires_dist:
  - wcwidth
  requires_python: '>=3.7.0'
- kind: pypi
  name: protobuf
  version: 3.20.3
  url: https://files.pythonhosted.org/packages/31/be/80a9c6f16dfa4d41be3edbe655349778ae30882407fa8275eb46b4d34854/protobuf-3.20.3-cp310-cp310-manylinux_2_12_x86_64.manylinux2010_x86_64.whl
  sha256: 9aae4406ea63d825636cc11ffb34ad3379335803216ee3a856787bcf5ccc751e
  requires_python: '>=3.7'
- kind: pypi
  name: psutil
  version: 5.9.8
  url: https://files.pythonhosted.org/packages/c5/4f/0e22aaa246f96d6ac87fe5ebb9c5a693fbe8877f537a1022527c47ca43c5/psutil-5.9.8-cp36-abi3-manylinux_2_12_x86_64.manylinux2010_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: d06016f7f8625a1825ba3732081d77c94589dca78b7a3fc072194851e88461a4
  requires_dist:
  - ipaddress ; python_version < '3.0' and extra == 'test'
  - mock ; python_version < '3.0' and extra == 'test'
  - enum34 ; python_version <= '3.4' and extra == 'test'
  - pywin32 ; sys_platform == 'win32' and extra == 'test'
  - wmi ; sys_platform == 'win32' and extra == 'test'
  requires_python: '>=2.7,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*,!=3.5.*'
- kind: conda
  name: pthread-stubs
  version: '0.4'
  build: h36c2ea0_1001
  build_number: 1001
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-h36c2ea0_1001.tar.bz2
  sha256: 67c84822f87b641d89df09758da498b2d4558d47b920fd1d3fe6d3a871e000ff
  md5: 22dad4df6e8630e8dff2428f6f6a7036
  depends:
  - libgcc-ng >=7.5.0
  license: MIT
  license_family: MIT
  size: 5625
  timestamp: 1606147468727
- kind: pypi
  name: ptyprocess
  version: 0.7.0
  url: https://files.pythonhosted.org/packages/22/a6/858897256d0deac81a172289110f31629fc4cee19b6f01283303e18c8db3/ptyprocess-0.7.0-py2.py3-none-any.whl
  sha256: 4b41f3967fce3af57cc7e94b888626c18bf37a083e3651ca8feeb66d492fef35
- kind: conda
  name: pugixml
  version: '1.14'
  build: h59595ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/pugixml-1.14-h59595ed_0.conda
  sha256: ea5f2d593177318f6b19af05018c953f41124cbb3bf21f9fdedfdb6ac42913ae
  md5: 2c97dd90633508b422c11bd3018206ab
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  size: 114871
  timestamp: 1696182708943
- kind: conda
  name: pulseaudio-client
  version: '17.0'
  build: hb77b528_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/pulseaudio-client-17.0-hb77b528_0.conda
  sha256: b27c0c8671bd95c205a61aeeac807c095b60bc76eb5021863f919036d7a964fc
  md5: 07f45f1be1c25345faddb8db0de8039b
  depends:
  - dbus >=1.13.6,<2.0a0
  - libgcc-ng >=12
  - libglib >=2.78.3,<3.0a0
  - libsndfile >=1.2.2,<1.3.0a0
  - libsystemd0 >=255
  constrains:
  - pulseaudio 17.0 *_0
  license: LGPL-2.1-or-later
  license_family: LGPL
  size: 757633
  timestamp: 1705690081905
- kind: pypi
  name: pure-eval
  version: 0.2.2
  url: https://files.pythonhosted.org/packages/2b/27/77f9d5684e6bce929f5cfe18d6cfbe5133013c06cb2fbf5933670e60761d/pure_eval-0.2.2-py3-none-any.whl
  sha256: 01eaab343580944bc56080ebe0a674b39ec44a945e6d09ba7db3cb8cec289350
  requires_dist:
  - pytest ; extra == 'tests'
- kind: conda
  name: pyarrow
  version: 15.0.2
  build: py310hd207890_6_cpu
  build_number: 6
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/pyarrow-15.0.2-py310hd207890_6_cpu.conda
  sha256: 0fef761f539cbd3a8f145163921e53d3928ebe3953e548d679862c357b580c12
  md5: b00e0408587fecc209714eff65406892
  depends:
  - libarrow 15.0.2 hefa796f_6_cpu
  - libarrow-acero 15.0.2 hbabe93e_6_cpu
  - libarrow-dataset 15.0.2 hbabe93e_6_cpu
  - libarrow-flight 15.0.2 hc4f8a93_6_cpu
  - libarrow-flight-sql 15.0.2 he4f5ca8_6_cpu
  - libarrow-gandiva 15.0.2 hc1954e9_6_cpu
  - libarrow-substrait 15.0.2 he4f5ca8_6_cpu
  - libgcc-ng >=12
  - libparquet 15.0.2 hacf5a1f_6_cpu
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  - numpy >=1.22.4,<2.0a0
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  constrains:
  - apache-arrow-proc =*=cpu
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/pyarrow
  size: 4481274
  timestamp: 1714448787064
- kind: pypi
  name: pycocotools
  version: 2.0.7
  url: https://files.pythonhosted.org/packages/ba/64/0451cf41a00fd5ac4501de4ea0e395b7d909e09d665e56890b5d3809ae26/pycocotools-2.0.7-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: 9eb5d46900375adaba88eedb5cbc29d8cbcf43e82505d67378df1c3b720a8c5f
  requires_dist:
  - matplotlib>=2.1.0
  - numpy
  requires_python: '>=3.5'
- kind: pypi
  name: pycollada
  version: '0.8'
  url: https://files.pythonhosted.org/packages/dc/f1/5e81108414287278a01f1642271d7885e2aebc2bd10e7cf744d8c4cf0955/pycollada-0.8.tar.gz
  sha256: f3a3759cc4cec1d59e932aad74399dbcf541d18862aad903c770040da42af20e
  requires_dist:
  - python-dateutil>=2.2
  - numpy
  - lxml ; extra == 'prettyprint'
  - lxml ; extra == 'validation'
- kind: pypi
  name: pycparser
  version: '2.22'
  url: https://files.pythonhosted.org/packages/13/a3/a812df4e2dd5696d1f351d58b8fe16a405b234ad2886a0dab9183fb78109/pycparser-2.22-py3-none-any.whl
  sha256: c3702b6d3dd8c7abc1afa565d7e63d53a1d0bd86cdc24edd75470f4de499cfcc
  requires_python: '>=3.8'
- kind: pypi
  name: pygments
  version: 2.18.0
  url: https://files.pythonhosted.org/packages/f7/3f/01c8b82017c199075f8f788d0d906b9ffbbc5a47dc9918a945e13d5a2bda/pygments-2.18.0-py3-none-any.whl
  sha256: b8e6aca0523f3ab76fee51799c488e38782ac06eafcf95e7ba832985c8e7b13a
  requires_dist:
  - colorama>=0.4.6 ; extra == 'windows-terminal'
  requires_python: '>=3.8'
- kind: pypi
  name: pyliblzfse
  version: 0.4.1
  url: https://files.pythonhosted.org/packages/2c/ba/a4bc465d36f6aafbff89da1bf67bcc6a97475b1d2300a74a778dcb293cef/pyliblzfse-0.4.1.tar.gz
  sha256: bb0b899b3830c02fdf3dbde48ea59611833f366fef836e5c32cf8145134b7d3d
- kind: pypi
  name: pymeshlab
  version: 2022.2.post3
  url: https://files.pythonhosted.org/packages/44/b2/5e98847b924748ec293bac6a68bb7d203a9ec328dbf7ef9fb886fd0e2c07/pymeshlab-2022.2.post3-cp310-cp310-manylinux1_x86_64.whl
  sha256: b329ce9d42eec47bd3262c61d50d3e29e2b19defe3cb981e9ed0c0b14d7a1393
  requires_dist:
  - numpy
- kind: pypi
  name: pyngrok
  version: 7.1.6
  url: https://files.pythonhosted.org/packages/cb/55/68b89d526e8331724665dcded0a32a76d73d6bcac41cc56084fda8e25486/pyngrok-7.1.6-py3-none-any.whl
  sha256: 422ac7c339622fef51308f0c493a1f5a05d0f403eee5bdd183fb4021a6cb90d4
  requires_dist:
  - pyyaml>=5.1
  - coverage[toml] ; extra == 'dev'
  - psutil ; extra == 'dev'
  - flake8 ; extra == 'dev'
  - flake8-pyproject ; extra == 'dev'
  - pep8-naming ; extra == 'dev'
  - sphinx ; extra == 'docs'
  - sphinx-notfound-page ; extra == 'docs'
  - sphinx-autodoc-typehints==1.25.2 ; extra == 'docs'
  - sphinx-substitution-extensions ; extra == 'docs'
  - mypy ; extra == 'docs'
  - types-pyyaml ; extra == 'docs'
  requires_python: '>=3.8'
- kind: pypi
  name: pyparsing
  version: 3.1.2
  url: https://files.pythonhosted.org/packages/9d/ea/6d76df31432a0e6fdf81681a895f009a4bb47b3c39036db3e1b528191d52/pyparsing-3.1.2-py3-none-any.whl
  sha256: f9db75911801ed778fe61bb643079ff86601aca99fcae6345aa67292038fb742
  requires_dist:
  - railroad-diagrams ; extra == 'diagrams'
  - jinja2 ; extra == 'diagrams'
  requires_python: '>=3.6.8'
- kind: pypi
  name: pyquaternion
  version: 0.9.9
  url: https://files.pythonhosted.org/packages/49/b3/d8482e8cacc8ea15a356efea13d22ce1c5914a9ee36622ba250523240bf2/pyquaternion-0.9.9-py3-none-any.whl
  sha256: e65f6e3f7b1fdf1a9e23f82434334a1ae84f14223eee835190cd2e841f8172ec
  requires_dist:
  - numpy
  - mkdocs ; extra == 'dev'
  - nose ; extra == 'test'
- kind: conda
  name: pysocks
  version: 1.7.1
  build: pyha2e5f31_6
  build_number: 6
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha2e5f31_6.tar.bz2
  sha256: a42f826e958a8d22e65b3394f437af7332610e43ee313393d1cf143f0a2d274b
  md5: 2a7de29fb590ca14b5243c4c812c8025
  depends:
  - __unix
  - python >=3.8
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pysocks
  size: 18981
  timestamp: 1661604969727
- kind: conda
  name: python
  version: 3.10.14
  build: hd12c33a_0_cpython
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/python-3.10.14-hd12c33a_0_cpython.conda
  sha256: 76a5d12e73542678b70a94570f7b0f7763f9a938f77f0e75d9ea615ef22aa84c
  md5: 2b4ba962994e8bd4be9ff5b64b75aff2
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-64 >=2.36.1
  - libffi >=3.4,<4.0a0
  - libgcc-ng >=12
  - libnsl >=2.0.1,<2.1.0a0
  - libsqlite >=3.45.2,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libxcrypt >=4.4.36
  - libzlib >=1.2.13,<1.3.0a0
  - ncurses >=6.4.20240210,<7.0a0
  - openssl >=3.2.1,<4.0a0
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  - xz >=5.2.6,<6.0a0
  constrains:
  - python_abi 3.10.* *_cp310
  license: Python-2.0
  size: 25517742
  timestamp: 1710939725109
- kind: pypi
  name: python-box
  version: 6.1.0
  url: https://files.pythonhosted.org/packages/6f/32/3c865e7d62e481c46abffef3303db0d27bf2ca72e4f497d05d66939bfd4a/python_box-6.1.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: ab13208b053525ef154a36a4a52873b98a12b18b946edd4c939a4d5080e9a218
  requires_dist:
  - pyyaml ; extra == 'pyyaml'
  - ruamel-yaml>=0.17 ; extra == 'all'
  - toml ; extra == 'all'
  - msgpack ; extra == 'all'
  - msgpack ; extra == 'msgpack'
  - ruamel-yaml>=0.17 ; extra == 'ruamel-yaml'
  - toml ; extra == 'toml'
  - tomli-w ; extra == 'tomli'
  - tomli ; python_version < '3.11' and extra == 'tomli'
  - ruamel-yaml>=0.17 ; extra == 'yaml'
  requires_python: '>=3.7'
- kind: pypi
  name: python-dateutil
  version: 2.9.0.post0
  url: https://files.pythonhosted.org/packages/ec/57/56b9bcc3c9c6a792fcbaf139543cee77261f3651ca9da0c93f5c1221264b/python_dateutil-2.9.0.post0-py2.py3-none-any.whl
  sha256: a8b2bc7bffae282281c8140a97d3aa9c14da0b136dfe83f850eea9a5f7470427
  requires_dist:
  - six>=1.5
  requires_python: '!=3.0.*,!=3.1.*,!=3.2.*,>=2.7'
- kind: pypi
  name: python-engineio
  version: 4.9.0
  url: https://files.pythonhosted.org/packages/fe/e5/03fa8e76c718e1dd7fb5b74e9ce816ae0e08734080b1e98dbafbcf2fc8ba/python_engineio-4.9.0-py3-none-any.whl
  sha256: 979859bff770725b75e60353d7ae53b397e8b517d05ba76733b404a3dcca3e4c
  requires_dist:
  - simple-websocket>=0.10.0
  - aiohttp>=3.4 ; extra == 'asyncio-client'
  - requests>=2.21.0 ; extra == 'client'
  - websocket-client>=0.54.0 ; extra == 'client'
  - sphinx ; extra == 'docs'
  requires_python: '>=3.6'
- kind: pypi
  name: python-json-logger
  version: 2.0.7
  url: https://files.pythonhosted.org/packages/35/a6/145655273568ee78a581e734cf35beb9e33a370b29c5d3c8fee3744de29f/python_json_logger-2.0.7-py3-none-any.whl
  sha256: f380b826a991ebbe3de4d897aeec42760035ac760345e57b812938dc8b35e2bd
  requires_python: '>=3.6'
- kind: pypi
  name: python-socketio
  version: 5.11.2
  url: https://files.pythonhosted.org/packages/af/bf/be12875b17709b591d8505811e513a88b31316e4ce0e801da351b4765ea5/python_socketio-5.11.2-py3-none-any.whl
  sha256: b9f22a8ff762d7a6e123d16a43ddb1a27d50f07c3c88ea999334f2f89b0ad52b
  requires_dist:
  - bidict>=0.21.0
  - python-engineio>=4.8.0
  - aiohttp>=3.4 ; extra == 'asyncio-client'
  - requests>=2.21.0 ; extra == 'client'
  - websocket-client>=0.54.0 ; extra == 'client'
  - sphinx ; extra == 'docs'
  requires_python: '>=3.8'
- kind: conda
  name: python_abi
  version: '3.10'
  build: 4_cp310
  build_number: 4
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/python_abi-3.10-4_cp310.conda
  sha256: 456bec815bfc2b364763084d08b412fdc4c17eb9ccc66a36cb775fa7ac3cbaec
  md5: 26322ec5d7712c3ded99dd656142b8ce
  constrains:
  - python 3.10.* *_cpython
  license: BSD-3-Clause
  license_family: BSD
  size: 6398
  timestamp: 1695147363189
- kind: conda
  name: pytorch
  version: 2.2.2
  build: py3.10_cuda11.8_cudnn8.7.0_0
  subdir: linux-64
  url: https://conda.anaconda.org/pytorch/linux-64/pytorch-2.2.2-py3.10_cuda11.8_cudnn8.7.0_0.tar.bz2
  sha256: b5ade394eae124c0e57f602b789f94def2c2a9eea1556c6ee13fc354ed7e6212
  md5: 9b4b26675d6cea4197f0b680752a784d
  depends:
  - blas * mkl
  - filelock
  - jinja2
  - llvm-openmp <16
  - mkl >=2018
  - networkx
  - python >=3.10,<3.11.0a0
  - pytorch-cuda >=11.8,<11.9
  - pytorch-mutex 1.0 cuda
  - pyyaml
  - sympy
  - torchtriton 2.2.0
  - typing_extensions
  constrains:
  - cpuonly <0
  license: BSD 3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/torch
  size: **********
  timestamp: 1711411702923
- kind: conda
  name: pytorch-cuda
  version: '11.8'
  build: h7e8668a_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/pytorch/linux-64/pytorch-cuda-11.8-h7e8668a_5.tar.bz2
  sha256: 81a9df218f84b45386818dbc180180a5adb7a5df097d1c4c7ec05c5fa5b0f8ca
  md5: 48e990086eb245cce92f09b45a34651e
  depends:
  - cuda-cudart >=11.8,<12.0
  - cuda-cupti >=11.8,<12.0
  - cuda-libraries >=11.8,<12.0
  - cuda-nvrtc >=11.8,<12.0
  - cuda-nvtx >=11.8,<12.0
  - cuda-runtime >=11.8,<12.0
  - libcublas >=*********,<**********
  - libcufft >=*********,<*********
  - libcusolver >=*********,<*********
  - libcusparse >=*********,<*********
  - libnpp >=*********,<*********
  - libnvjpeg >=*********,<*********
  size: 3561
  timestamp: 1682528151734
- kind: pypi
  name: pytorch-msssim
  version: 1.0.0
  url: https://files.pythonhosted.org/packages/e2/8c/856047f955acc30179e9255fdc488059ca22f0938519523d53494f7cfee8/pytorch_msssim-1.0.0-py3-none-any.whl
  sha256: 0b4b7bbf7035fe9dc8084244237aac13b1f104852c45b63a7e9fab4363bede54
  requires_dist:
  - torch
- kind: conda
  name: pytorch-mutex
  version: '1.0'
  build: cuda
  build_number: 100
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/pytorch/noarch/pytorch-mutex-1.0-cuda.tar.bz2
  sha256: c16316183f51b74ca5eee4dcb8631052f328c0bbf244176734a0b7d390b81ee3
  md5: a948316e36fb5b11223b3fcfa93f8358
  size: 2906
  timestamp: 1628062930777
- kind: pypi
  name: pytz
  version: '2024.1'
  url: https://files.pythonhosted.org/packages/9c/3d/a121f284241f08268b21359bd425f7d4825cffc5ac5cd0e1b3d82ffd2b10/pytz-2024.1-py2.py3-none-any.whl
  sha256: 328171f4e3623139da4983451950b28e95ac706e13f3f2630a879749e7a8b319
- kind: conda
  name: pyyaml
  version: 6.0.1
  build: py310h2372a71_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/pyyaml-6.0.1-py310h2372a71_1.conda
  sha256: aa78ccddb0a75fa722f0f0eb3537c73ee1219c9dd46cea99d6b9eebfdd780f3d
  md5: bb010e368de4940771368bc3dc4c63e7
  depends:
  - libgcc-ng >=12
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  - yaml >=0.2.5,<0.3.0a0
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pyyaml
  size: 170627
  timestamp: 1695373587159
- kind: pypi
  name: pyzmq
  version: 26.0.3
  url: https://files.pythonhosted.org/packages/40/4f/088d0fe18b188a0754483b7d632a97ef608dce80c2648219d071c9f1715c/pyzmq-26.0.3-cp310-cp310-manylinux_2_28_x86_64.whl
  sha256: ba6e5e6588e49139a0979d03a7deb9c734bde647b9a8808f26acf9c547cab1bf
  requires_dist:
  - cffi ; implementation_name == 'pypy'
  requires_python: '>=3.7'
- kind: conda
  name: qt-main
  version: 5.15.8
  build: hc9dc06e_21
  build_number: 21
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/qt-main-5.15.8-hc9dc06e_21.conda
  sha256: 6b4594f6f2fad65a7ed52993f602e3ab183193755fe4a492aaa48e463b23105b
  md5: b325046180590c868ce0dbf267b82eb8
  depends:
  - __glibc >=2.17,<3.0.a0
  - alsa-lib >=1.2.11,<1.3.0a0
  - dbus >=1.13.6,<2.0a0
  - fontconfig >=2.14.2,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - gst-plugins-base >=1.24.1,<1.25.0a0
  - gstreamer >=1.24.1,<1.25.0a0
  - harfbuzz >=8.3.0,<9.0a0
  - icu >=73.2,<74.0a0
  - krb5 >=1.21.2,<1.22.0a0
  - libclang-cpp15 >=15.0.7,<15.1.0a0
  - libclang13 >=15.0.7
  - libcups >=2.3.3,<2.4.0a0
  - libevent >=2.1.12,<2.1.13.0a0
  - libexpat >=2.6.2,<3.0a0
  - libgcc-ng >=12
  - libglib >=2.80.0,<3.0a0
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libllvm15 >=15.0.7,<15.1.0a0
  - libpng >=1.6.43,<1.7.0a0
  - libpq >=16.2,<17.0a0
  - libsqlite >=3.45.2,<4.0a0
  - libstdcxx-ng >=12
  - libxcb >=1.15,<1.16.0a0
  - libxkbcommon >=1.7.0,<2.0a0
  - libxml2 >=2.12.6,<3.0a0
  - libzlib >=1.2.13,<1.3.0a0
  - mysql-libs >=8.3.0,<8.4.0a0
  - nspr >=4.35,<5.0a0
  - nss >=3.98,<4.0a0
  - openssl >=3.2.1,<4.0a0
  - pulseaudio-client >=17.0,<17.1.0a0
  - xcb-util >=0.4.0,<0.5.0a0
  - xcb-util-image >=0.4.0,<0.5.0a0
  - xcb-util-keysyms >=0.4.0,<0.5.0a0
  - xcb-util-renderutil >=0.3.9,<0.4.0a0
  - xcb-util-wm >=0.4.1,<0.5.0a0
  - xorg-libice >=1.1.1,<2.0a0
  - xorg-libsm >=1.2.4,<2.0a0
  - xorg-libx11 >=1.8.9,<2.0a0
  - xorg-libxext >=1.3.4,<2.0a0
  - xorg-xf86vidmodeproto
  - zstd >=1.5.5,<1.6.0a0
  constrains:
  - qt 5.15.8
  license: LGPL-3.0-only
  license_family: LGPL
  size: 61305384
  timestamp: 1712549380352
- kind: pypi
  name: qtconsole
  version: 5.5.2
  url: https://files.pythonhosted.org/packages/f2/3f/de5e5eb44900c1ed1c1567bc505e3b6e6f4c01cf29e558bf2f8cee29af5b/qtconsole-5.5.2-py3-none-any.whl
  sha256: 42d745f3d05d36240244a04e1e1ec2a86d5d9b6edb16dbdef582ccb629e87e0b
  requires_dist:
  - traitlets!=5.2.1,!=5.2.2
  - jupyter-core
  - jupyter-client>=4.1
  - pygments
  - ipykernel>=4.1
  - qtpy>=2.4.0
  - pyzmq>=17.1
  - packaging
  - sphinx>=1.3 ; extra == 'doc'
  - flaky ; extra == 'test'
  - pytest ; extra == 'test'
  - pytest-qt ; extra == 'test'
  requires_python: '>=3.8'
- kind: pypi
  name: qtpy
  version: 2.4.1
  url: https://files.pythonhosted.org/packages/7e/a9/2146d5117ad8a81185331e0809a6b48933c10171f5bac253c6df9fce991c/QtPy-2.4.1-py3-none-any.whl
  sha256: 1c1d8c4fa2c884ae742b069151b0abe15b3f70491f3972698c683b8e38de839b
  requires_dist:
  - packaging
  - pytest!=7.0.0,!=7.0.1,>=6 ; extra == 'test'
  - pytest-cov>=3.0.0 ; extra == 'test'
  - pytest-qt ; extra == 'test'
  requires_python: '>=3.7'
- kind: pypi
  name: rawpy
  version: 0.21.0
  url: https://files.pythonhosted.org/packages/72/2f/80c24a0f24c3a8ae2a9b8cfb579bba0ddcdef9bfd420b98ce69a51b754d2/rawpy-0.21.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: d6915760945ca058b9b1b2184459a862baa33b70e53abeb165d2f3d779eb7a78
  requires_dist:
  - numpy
- kind: conda
  name: rdma-core
  version: '51.0'
  build: hd3aeb46_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/rdma-core-51.0-hd3aeb46_0.conda
  sha256: bcc774b60605b09701cfad41b2d6d9c3f052dd4adfc1f02bf1c929076f48fe30
  md5: 493598e1f28c01e316fda127715593aa
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libnl >=3.9.0,<4.0a0
  - libstdcxx-ng >=12
  license: Linux-OpenIB
  license_family: BSD
  size: 4734659
  timestamp: 1711958296706
- kind: conda
  name: re2
  version: 2023.09.01
  build: h7f4b329_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/re2-2023.09.01-h7f4b329_2.conda
  sha256: f0f520f57e6b58313e8c41abc7dfa48742a05f1681f05654558127b667c769a8
  md5: 8f70e36268dea8eb666ef14c29bd3cda
  depends:
  - libre2-11 2023.09.01 h5a48ba9_2
  license: BSD-3-Clause
  license_family: BSD
  size: 26617
  timestamp: 1708946796423
- kind: conda
  name: readline
  version: '8.2'
  build: h8228510_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8228510_1.conda
  sha256: 5435cf39d039387fbdc977b0a762357ea909a7694d9528ab40f005e9208744d7
  md5: 47d31b792659ce70f470b5c82fdfb7a4
  depends:
  - libgcc-ng >=12
  - ncurses >=6.3,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  size: 281456
  timestamp: 1679532220005
- kind: pypi
  name: referencing
  version: 0.35.1
  url: https://files.pythonhosted.org/packages/b7/59/2056f61236782a2c86b33906c025d4f4a0b17be0161b63b70fd9e8775d36/referencing-0.35.1-py3-none-any.whl
  sha256: eda6d3234d62814d1c64e305c1331c9a3a6132da475ab6382eaa997b21ee75de
  requires_dist:
  - attrs>=22.2.0
  - rpds-py>=0.7.0
  requires_python: '>=3.8'
- kind: conda
  name: requests
  version: 2.31.0
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/requests-2.31.0-pyhd8ed1ab_0.conda
  sha256: 9f629d6fd3c8ac5f2a198639fe7af87c4db2ac9235279164bfe0fcb49d8c4bad
  md5: a30144e4156cdbb236f99ebb49828f8b
  depends:
  - certifi >=2017.4.17
  - charset-normalizer >=2,<4
  - idna >=2.5,<4
  - python >=3.7
  - urllib3 >=1.21.1,<3
  constrains:
  - chardet >=3.0.2,<6
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/requests
  size: 56690
  timestamp: 1684774408600
- kind: pypi
  name: requests-toolbelt
  version: 1.0.0
  url: https://files.pythonhosted.org/packages/3f/51/d4db610ef29373b879047326cbf6fa98b6c1969d6f6dc423279de2b1be2c/requests_toolbelt-1.0.0-py2.py3-none-any.whl
  sha256: cccfdd665f0a24fcf4726e690f65639d272bb0637b9b92dfd91a5568ccf6bd06
  requires_dist:
  - requests<3.0.0,>=2.0.1
  requires_python: '>=2.7,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*'
- kind: pypi
  name: retrying
  version: 1.3.4
  url: https://files.pythonhosted.org/packages/8f/04/9e36f28be4c0532c0e9207ff9dc01fb13a2b0eb036476a213b0000837d0e/retrying-1.3.4-py3-none-any.whl
  sha256: 8cc4d43cb8e1125e0ff3344e9de678fefd85db3b750b81b2240dc0183af37b35
  requires_dist:
  - six>=1.7.0
- kind: pypi
  name: rfc3339-validator
  version: 0.1.4
  url: https://files.pythonhosted.org/packages/7b/44/4e421b96b67b2daff264473f7465db72fbdf36a07e05494f50300cc7b0c6/rfc3339_validator-0.1.4-py2.py3-none-any.whl
  sha256: 24f6ec1eda14ef823da9e36ec7113124b39c04d50a4d3d3a3c2859577e7791fa
  requires_dist:
  - six
  requires_python: '>=2.7,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*'
- kind: pypi
  name: rfc3986-validator
  version: 0.1.1
  url: https://files.pythonhosted.org/packages/9e/51/17023c0f8f1869d8806b979a2bffa3f861f26a3f1a66b094288323fba52f/rfc3986_validator-0.1.1-py2.py3-none-any.whl
  sha256: 2f235c432ef459970b4306369336b9d5dbdda31b510ca1e327636e01f528bfa9
  requires_python: '>=2.7,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*'
- kind: pypi
  name: rich
  version: 13.7.1
  url: https://files.pythonhosted.org/packages/87/67/a37f6214d0e9fe57f6ae54b2956d550ca8365857f42a1ce0392bb21d9410/rich-13.7.1-py3-none-any.whl
  sha256: 4edbae314f59eb482f54e9e30bf00d33350aaa94f4bfcd4e9e3110e64d0d7222
  requires_dist:
  - ipywidgets>=7.5.1,<9 ; extra == 'jupyter'
  - markdown-it-py>=2.2.0
  - pygments>=2.13.0,<3.0.0
  - typing-extensions>=4.0.0,<5.0 ; python_version < '3.9'
  requires_python: '>=3.7.0'
- kind: pypi
  name: rpds-py
  version: 0.18.1
  url: https://files.pythonhosted.org/packages/e5/20/10c12b1acb102c4981a7e1dc86b60e36c1d5c940a7bda48643542f80dbff/rpds_py-0.18.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: d70129cef4a8d979caa37e7fe957202e7eee8ea02c5e16455bc9808a59c6b2f0
  requires_python: '>=3.8'
- kind: pypi
  name: rtree
  version: 1.2.0
  url: https://files.pythonhosted.org/packages/59/a5/176d27468a1b0bcd7fa9c011cadacfa364e9bca8fa649baab7fb3f15af70/Rtree-1.2.0-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
  sha256: 613f2158aeba6fcb5e4aa4c076bb6de85e20c4f9fb0a8b426a71d6ff5846795b
  requires_python: '>=3.8'
- kind: conda
  name: s2n
  version: 1.4.12
  build: h06160fa_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/s2n-1.4.12-h06160fa_0.conda
  sha256: fc5759c4d8136bb9048ed5cd2e8fd1a375104c3a7ec60fee1be0b06e7487d610
  md5: bf1899cfd6dea061a220fa7e96a1f4bd
  depends:
  - libgcc-ng >=12
  - openssl >=3.2.1,<4.0a0
  license: Apache-2.0
  license_family: Apache
  size: 346689
  timestamp: 1713325107791
- kind: pypi
  name: scikit-image
  version: 0.23.2
  url: https://files.pythonhosted.org/packages/ea/82/264b5d0f2f4c4ec073e59dd1ac1ed5fa85f54bec2dd4cc231fcdfd12ba42/scikit_image-0.23.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: 08b10781efbd6b084f3c847ff4049b657241ea866b9e331b14bf791dcb3e6661
  requires_dist:
  - numpy>=1.23
  - scipy>=1.9
  - networkx>=2.8
  - pillow>=9.1
  - imageio>=2.33
  - tifffile>=2022.8.12
  - packaging>=21
  - lazy-loader>=0.4
  - meson-python>=0.15 ; extra == 'build'
  - wheel ; extra == 'build'
  - setuptools>=67 ; extra == 'build'
  - packaging>=21 ; extra == 'build'
  - ninja ; extra == 'build'
  - cython>=3.0.4 ; extra == 'build'
  - pythran ; extra == 'build'
  - numpy>=2.0.0rc1 ; extra == 'build'
  - spin==0.8 ; extra == 'build'
  - build ; extra == 'build'
  - pooch>=1.6.0 ; extra == 'data'
  - pre-commit ; extra == 'developer'
  - ipython ; extra == 'developer'
  - tomli ; python_version < '3.11' and extra == 'developer'
  - sphinx>=7.3 ; extra == 'docs'
  - sphinx-gallery>=0.14 ; extra == 'docs'
  - numpydoc>=1.7 ; extra == 'docs'
  - sphinx-copybutton ; extra == 'docs'
  - pytest-runner ; extra == 'docs'
  - matplotlib>=3.6 ; extra == 'docs'
  - dask[array]>=2022.9.2 ; extra == 'docs'
  - pandas>=1.5 ; extra == 'docs'
  - seaborn>=0.11 ; extra == 'docs'
  - pooch>=1.6 ; extra == 'docs'
  - tifffile>=2022.8.12 ; extra == 'docs'
  - myst-parser ; extra == 'docs'
  - ipywidgets ; extra == 'docs'
  - ipykernel ; extra == 'docs'
  - plotly>=5.10 ; extra == 'docs'
  - kaleido ; extra == 'docs'
  - scikit-learn>=1.1 ; extra == 'docs'
  - sphinx-design>=0.5 ; extra == 'docs'
  - pydata-sphinx-theme>=0.15.2 ; extra == 'docs'
  - pywavelets>=1.1.1 ; extra == 'docs'
  - pytest-doctestplus ; extra == 'docs'
  - simpleitk ; extra == 'optional'
  - astropy>=5.0 ; extra == 'optional'
  - cloudpickle>=0.2.1 ; extra == 'optional'
  - dask[array]>=2021.1.0 ; extra == 'optional'
  - matplotlib>=3.6 ; extra == 'optional'
  - pooch>=1.6.0 ; extra == 'optional'
  - pyamg ; extra == 'optional'
  - pywavelets>=1.1.1 ; extra == 'optional'
  - scikit-learn>=1.1 ; extra == 'optional'
  - asv ; extra == 'test'
  - numpydoc>=1.7 ; extra == 'test'
  - pooch>=1.6.0 ; extra == 'test'
  - pytest>=7.0 ; extra == 'test'
  - pytest-cov>=2.11.0 ; extra == 'test'
  - pytest-localserver ; extra == 'test'
  - pytest-faulthandler ; extra == 'test'
  - pytest-doctestplus ; extra == 'test'
  requires_python: '>=3.10'
- kind: pypi
  name: scikit-learn
  version: 1.4.2
  url: https://files.pythonhosted.org/packages/8f/38/420ee614359d8f453ffe2bb5c2e963bf50459d9bbd3f5a92aa9059658955/scikit_learn-1.4.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: 36f0ea5d0f693cb247a073d21a4123bdf4172e470e6d163c12b74cbb1536cf38
  requires_dist:
  - numpy>=1.19.5
  - scipy>=1.6.0
  - joblib>=1.2.0
  - threadpoolctl>=2.0.0
  - matplotlib>=3.3.4 ; extra == 'benchmark'
  - pandas>=1.1.5 ; extra == 'benchmark'
  - memory-profiler>=0.57.0 ; extra == 'benchmark'
  - matplotlib>=3.3.4 ; extra == 'docs'
  - scikit-image>=0.17.2 ; extra == 'docs'
  - pandas>=1.1.5 ; extra == 'docs'
  - seaborn>=0.9.0 ; extra == 'docs'
  - memory-profiler>=0.57.0 ; extra == 'docs'
  - sphinx>=6.0.0 ; extra == 'docs'
  - sphinx-copybutton>=0.5.2 ; extra == 'docs'
  - sphinx-gallery>=0.15.0 ; extra == 'docs'
  - numpydoc>=1.2.0 ; extra == 'docs'
  - pillow>=7.1.2 ; extra == 'docs'
  - pooch>=1.6.0 ; extra == 'docs'
  - sphinx-prompt>=1.3.0 ; extra == 'docs'
  - sphinxext-opengraph>=0.4.2 ; extra == 'docs'
  - plotly>=5.14.0 ; extra == 'docs'
  - matplotlib>=3.3.4 ; extra == 'examples'
  - scikit-image>=0.17.2 ; extra == 'examples'
  - pandas>=1.1.5 ; extra == 'examples'
  - seaborn>=0.9.0 ; extra == 'examples'
  - pooch>=1.6.0 ; extra == 'examples'
  - plotly>=5.14.0 ; extra == 'examples'
  - matplotlib>=3.3.4 ; extra == 'tests'
  - scikit-image>=0.17.2 ; extra == 'tests'
  - pandas>=1.1.5 ; extra == 'tests'
  - pytest>=7.1.2 ; extra == 'tests'
  - pytest-cov>=2.9.0 ; extra == 'tests'
  - ruff>=0.0.272 ; extra == 'tests'
  - black>=23.3.0 ; extra == 'tests'
  - mypy>=1.3 ; extra == 'tests'
  - pyamg>=4.0.0 ; extra == 'tests'
  - polars>=0.19.12 ; extra == 'tests'
  - pyarrow>=12.0.0 ; extra == 'tests'
  - numpydoc>=1.2.0 ; extra == 'tests'
  - pooch>=1.6.0 ; extra == 'tests'
  requires_python: '>=3.9'
- kind: pypi
  name: scipy
  version: 1.13.0
  url: https://files.pythonhosted.org/packages/b9/9d/39dbcf49a793157f9d4f5b8961855677eb4dbb4b82700dcee7042ad2310c/scipy-1.13.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: b8434f6f3fa49f631fae84afee424e2483289dfc30a47755b4b4e6b07b2633a4
  requires_dist:
  - numpy<2.3,>=1.22.4
  - pytest ; extra == 'test'
  - pytest-cov ; extra == 'test'
  - pytest-timeout ; extra == 'test'
  - pytest-xdist ; extra == 'test'
  - asv ; extra == 'test'
  - mpmath ; extra == 'test'
  - gmpy2 ; extra == 'test'
  - threadpoolctl ; extra == 'test'
  - scikit-umfpack ; extra == 'test'
  - pooch ; extra == 'test'
  - hypothesis>=6.30 ; extra == 'test'
  - array-api-strict ; extra == 'test'
  - sphinx>=5.0.0 ; extra == 'doc'
  - pydata-sphinx-theme>=0.15.2 ; extra == 'doc'
  - sphinx-design>=0.4.0 ; extra == 'doc'
  - matplotlib>=3.5 ; extra == 'doc'
  - numpydoc ; extra == 'doc'
  - jupytext ; extra == 'doc'
  - myst-nb ; extra == 'doc'
  - pooch ; extra == 'doc'
  - jupyterlite-sphinx>=0.12.0 ; extra == 'doc'
  - jupyterlite-pyodide-kernel ; extra == 'doc'
  - mypy ; extra == 'dev'
  - typing-extensions ; extra == 'dev'
  - types-psutil ; extra == 'dev'
  - pycodestyle ; extra == 'dev'
  - ruff ; extra == 'dev'
  - cython-lint>=0.12.2 ; extra == 'dev'
  - rich-click ; extra == 'dev'
  - doit>=0.36.0 ; extra == 'dev'
  - pydevtool ; extra == 'dev'
  requires_python: '>=3.9'
- kind: pypi
  name: semantic-version
  version: 2.10.0
  url: https://files.pythonhosted.org/packages/6a/23/8146aad7d88f4fcb3a6218f41a60f6c2d4e3a72de72da1825dc7c8f7877c/semantic_version-2.10.0-py2.py3-none-any.whl
  sha256: de78a3b8e0feda74cabc54aab2da702113e33ac9d9eb9d2389bcf1f58b7d9177
  requires_dist:
  - django>=1.11 ; extra == 'dev'
  - nose2 ; extra == 'dev'
  - tox ; extra == 'dev'
  - check-manifest ; extra == 'dev'
  - coverage ; extra == 'dev'
  - flake8 ; extra == 'dev'
  - wheel ; extra == 'dev'
  - zest-releaser[recommended] ; extra == 'dev'
  - readme-renderer<25.0 ; python_version == '3.4' and extra == 'dev'
  - colorama<=0.4.1 ; python_version == '3.4' and extra == 'dev'
  - sphinx ; extra == 'doc'
  - sphinx-rtd-theme ; extra == 'doc'
  requires_python: '>=2.7'
- kind: pypi
  name: send2trash
  version: 1.8.3
  url: https://files.pythonhosted.org/packages/40/b0/4562db6223154aa4e22f939003cb92514c79f3d4dccca3444253fd17f902/Send2Trash-1.8.3-py3-none-any.whl
  sha256: 0c31227e0bd08961c7665474a3d1ef7193929fedda4233843689baa056be46c9
  requires_dist:
  - pyobjc-framework-cocoa ; sys_platform == 'darwin' and extra == 'nativelib'
  - pywin32 ; sys_platform == 'win32' and extra == 'nativelib'
  - pyobjc-framework-cocoa ; sys_platform == 'darwin' and extra == 'objc'
  - pywin32 ; sys_platform == 'win32' and extra == 'win32'
  requires_python: '!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*,!=3.5.*,>=2.7'
- kind: pypi
  name: sentry-sdk
  version: 2.1.1
  url: https://files.pythonhosted.org/packages/cf/58/cfbed3fdc41891e9d2b45a5dbe706dcaf35429eb1446e30a504bb2ea9ea2/sentry_sdk-2.1.1-py2.py3-none-any.whl
  sha256: 99aeb78fb76771513bd3b2829d12613130152620768d00cd3e45ac00cb17950f
  requires_dist:
  - urllib3>=1.26.11
  - certifi
  - aiohttp>=3.5 ; extra == 'aiohttp'
  - anthropic>=0.16 ; extra == 'anthropic'
  - arq>=0.23 ; extra == 'arq'
  - asyncpg>=0.23 ; extra == 'asyncpg'
  - apache-beam>=2.12 ; extra == 'beam'
  - bottle>=0.12.13 ; extra == 'bottle'
  - celery>=3 ; extra == 'celery'
  - celery-redbeat>=2 ; extra == 'celery-redbeat'
  - chalice>=1.16.0 ; extra == 'chalice'
  - clickhouse-driver>=0.2.0 ; extra == 'clickhouse-driver'
  - django>=1.8 ; extra == 'django'
  - falcon>=1.4 ; extra == 'falcon'
  - fastapi>=0.79.0 ; extra == 'fastapi'
  - flask>=0.11 ; extra == 'flask'
  - blinker>=1.1 ; extra == 'flask'
  - markupsafe ; extra == 'flask'
  - grpcio>=1.21.1 ; extra == 'grpcio'
  - httpx>=0.16.0 ; extra == 'httpx'
  - huey>=2 ; extra == 'huey'
  - huggingface-hub>=0.22 ; extra == 'huggingface-hub'
  - langchain>=0.0.210 ; extra == 'langchain'
  - loguru>=0.5 ; extra == 'loguru'
  - openai>=1.0.0 ; extra == 'openai'
  - tiktoken>=0.3.0 ; extra == 'openai'
  - opentelemetry-distro>=0.35b0 ; extra == 'opentelemetry'
  - opentelemetry-distro~=0.40b0 ; extra == 'opentelemetry-experimental'
  - opentelemetry-instrumentation-aiohttp-client~=0.40b0 ; extra == 'opentelemetry-experimental'
  - opentelemetry-instrumentation-django~=0.40b0 ; extra == 'opentelemetry-experimental'
  - opentelemetry-instrumentation-fastapi~=0.40b0 ; extra == 'opentelemetry-experimental'
  - opentelemetry-instrumentation-flask~=0.40b0 ; extra == 'opentelemetry-experimental'
  - opentelemetry-instrumentation-requests~=0.40b0 ; extra == 'opentelemetry-experimental'
  - opentelemetry-instrumentation-sqlite3~=0.40b0 ; extra == 'opentelemetry-experimental'
  - opentelemetry-instrumentation-urllib~=0.40b0 ; extra == 'opentelemetry-experimental'
  - pure-eval ; extra == 'pure-eval'
  - executing ; extra == 'pure-eval'
  - asttokens ; extra == 'pure-eval'
  - pymongo>=3.1 ; extra == 'pymongo'
  - pyspark>=2.4.4 ; extra == 'pyspark'
  - quart>=0.16.1 ; extra == 'quart'
  - blinker>=1.1 ; extra == 'quart'
  - rq>=0.6 ; extra == 'rq'
  - sanic>=0.8 ; extra == 'sanic'
  - sqlalchemy>=1.2 ; extra == 'sqlalchemy'
  - starlette>=0.19.1 ; extra == 'starlette'
  - starlite>=1.48 ; extra == 'starlite'
  - tornado>=5 ; extra == 'tornado'
  requires_python: '>=3.6'
- kind: pypi
  name: setproctitle
  version: 1.3.3
  url: https://files.pythonhosted.org/packages/79/e7/54b36be02aee8ad573be68f6f46fd62838735c2f007b22df50eb5e13a20d/setproctitle-1.3.3-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: fc74e84fdfa96821580fb5e9c0b0777c1c4779434ce16d3d62a9c4d8c710df39
  requires_dist:
  - pytest ; extra == 'test'
  requires_python: '>=3.7'
- kind: conda
  name: setuptools
  version: 69.5.1
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/setuptools-69.5.1-pyhd8ed1ab_0.conda
  sha256: 72d143408507043628b32bed089730b6d5f5445eccc44b59911ec9f262e365e7
  md5: 7462280d81f639363e6e63c81276bd9e
  depends:
  - python >=3.8
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/setuptools
  size: 501790
  timestamp: 1713094963112
- kind: pypi
  name: shapely
  version: 2.0.4
  url: https://files.pythonhosted.org/packages/81/77/e1475695606a8305c9ad5f5132d911abe8ed1655a6f5c817a69bdd2b5324/shapely-2.0.4-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: d2b4431f522b277c79c34b65da128029a9955e4481462cbf7ebec23aab61fc58
  requires_dist:
  - numpy<3,>=1.14
  - numpydoc==1.1.* ; extra == 'docs'
  - matplotlib ; extra == 'docs'
  - sphinx ; extra == 'docs'
  - sphinx-book-theme ; extra == 'docs'
  - sphinx-remove-toctrees ; extra == 'docs'
  - pytest ; extra == 'test'
  - pytest-cov ; extra == 'test'
  requires_python: '>=3.7'
- kind: pypi
  name: shtab
  version: 1.7.1
  url: https://files.pythonhosted.org/packages/e2/d1/a1d3189e7873408b9dc396aef0d7926c198b0df2aa3ddb5b539d3e89a70f/shtab-1.7.1-py3-none-any.whl
  sha256: 32d3d2ff9022d4c77a62492b6ec875527883891e33c6b479ba4d41a51e259983
  requires_dist:
  - pytest>=6 ; extra == 'dev'
  - pytest-cov ; extra == 'dev'
  - pytest-timeout ; extra == 'dev'
  requires_python: '>=3.7'
- kind: pypi
  name: simple-websocket
  version: 1.0.0
  url: https://files.pythonhosted.org/packages/6d/ea/288a8ac1d9551354488ff60c0ac6a76acc3b6b60f0460ac1944c75e240da/simple_websocket-1.0.0-py3-none-any.whl
  sha256: 1d5bf585e415eaa2083e2bcf02a3ecf91f9712e7b3e6b9fa0b461ad04e0837bc
  requires_dist:
  - wsproto
  - sphinx ; extra == 'docs'
  requires_python: '>=3.6'
- kind: pypi
  name: simplejson
  version: 3.19.2
  url: https://files.pythonhosted.org/packages/cb/b6/ed513a0adc3e2c9654864ffb68266dcab5720d5653428d690e7e4fb32a6c/simplejson-3.19.2-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: 18955c1da6fc39d957adfa346f75226246b6569e096ac9e40f67d102278c3bcb
  requires_python: '>=2.5,!=3.0.*,!=3.1.*,!=3.2.*'
- kind: pypi
  name: six
  version: 1.16.0
  url: https://files.pythonhosted.org/packages/d9/5a/e7c31adbe875f2abbb91bd84cf2dc52d792b5a01506781dbcf25c91daf11/six-1.16.0-py2.py3-none-any.whl
  sha256: 8abb2f1d86890a2dfb989f9a77cfcfd3e47c2a354b01111771326f8aa26e0254
  requires_python: '>=2.7,!=3.0.*,!=3.1.*,!=3.2.*'
- kind: pypi
  name: smmap
  version: 5.0.1
  url: https://files.pythonhosted.org/packages/a7/a5/10f97f73544edcdef54409f1d839f6049a0d79df68adbc1ceb24d1aaca42/smmap-5.0.1-py3-none-any.whl
  sha256: e6d8668fa5f93e706934a62d7b4db19c8d9eb8cf2adbb75ef1b675aa332b69da
  requires_python: '>=3.7'
- kind: conda
  name: snappy
  version: 1.2.0
  build: hdb0a2a9_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/snappy-1.2.0-hdb0a2a9_1.conda
  sha256: bb87116b8c6198f6979b3d212e9af12e08e12f2bf09970d0f9b4582607648b22
  md5: 843bbb8ace1d64ac50d64639ff38b014
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 42334
  timestamp: 1712591084054
- kind: pypi
  name: sniffio
  version: 1.3.1
  url: https://files.pythonhosted.org/packages/e9/44/75a9c9421471a6c4805dbf2356f7c181a29c1879239abab1ea2cc8f38b40/sniffio-1.3.1-py3-none-any.whl
  sha256: 2f6da418d1f1e0fddd844478f41680e794e6051915791a034ff65e5f100525a2
  requires_python: '>=3.7'
- kind: pypi
  name: soupsieve
  version: '2.5'
  url: https://files.pythonhosted.org/packages/4c/f3/038b302fdfbe3be7da016777069f26ceefe11a681055ea1f7817546508e3/soupsieve-2.5-py3-none-any.whl
  sha256: eaa337ff55a1579b6549dc679565eac1e3d000563bcb1c8ab0d0fefbc0c2cdc7
  requires_python: '>=3.8'
- kind: pypi
  name: splines
  version: 0.3.0
  url: https://files.pythonhosted.org/packages/cd/a4/518bee82ace2681327e471f8f59e9958760a564775a8f650489da61205d3/splines-0.3.0-py3-none-any.whl
  sha256: 8aadb623d8ada3adf47b6317b08f4cc953210f1bc1984c1cb0c298134d0e346d
  requires_dist:
  - numpy
  requires_python: '>=3.7'
- kind: pypi
  name: stack-data
  version: 0.6.3
  url: https://files.pythonhosted.org/packages/f1/7b/ce1eafaf1a76852e2ec9b22edecf1daa58175c090266e9f6c64afcd81d91/stack_data-0.6.3-py3-none-any.whl
  sha256: d5558e0c25a4cb0853cddad3d77da9891a08cb85dd9f9f91b9f8cd66e511e695
  requires_dist:
  - executing>=1.2.0
  - asttokens>=2.1.0
  - pure-eval
  - pytest ; extra == 'tests'
  - typeguard ; extra == 'tests'
  - pygments ; extra == 'tests'
  - littleutils ; extra == 'tests'
  - cython ; extra == 'tests'
- kind: conda
  name: suitesparse
  version: 5.10.1
  build: h5a4f163_3
  build_number: 3
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/suitesparse-5.10.1-h5a4f163_3.conda
  sha256: 235c9321cb76896f3304eea87248a74f52d8c088a38b9cbd98a5366e34756b90
  md5: f363554b9084fb9d5e3366fbbc0d18e0
  depends:
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libgcc-ng >=12
  - liblapack >=3.9.0,<4.0a0
  - libstdcxx-ng >=12
  - metis >=5.1.0,<5.1.1.0a0
  - mpfr >=4.2.1,<5.0a0
  - tbb >=2021.11.0
  license: LGPL-2.1-or-later AND BSD-3-Clause AND GPL-2.0-or-later AND Apache-2.0
  size: 1457359
  timestamp: 1705676854887
- kind: pypi
  name: svg-path
  version: '6.3'
  url: https://files.pythonhosted.org/packages/d6/ea/ec6101e1710ac74e88b10312e9b59734885155e47d7dbb1171e4d347a364/svg.path-6.3-py2.py3-none-any.whl
  sha256: 4bd4747679b527f8db9868e1623bee9f416540b658285d903885768d8a427e5a
  requires_dist:
  - pillow ; extra == 'test'
  - black ; extra == 'test'
  - check-manifest ; extra == 'test'
  - flake8 ; extra == 'test'
  - pyroma ; extra == 'test'
  - pytest ; extra == 'test'
  - pytest-cov ; extra == 'test'
  - zest-releaser[recommended] ; extra == 'test'
  requires_python: '>=3.8'
- kind: conda
  name: svt-av1
  version: 2.0.0
  build: h59595ed_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/svt-av1-2.0.0-h59595ed_0.conda
  sha256: eee484177184c7876d258917ab3f209396e6fc59e9bf3603a3ebf1ce8b39df80
  md5: 207e01ffa0eb2d2efb83fb6f46365a21
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 2633794
  timestamp: 1710374004661
- kind: conda
  name: sympy
  version: '1.12'
  build: pypyh9d50eac_103
  build_number: 103
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/sympy-1.12-pypyh9d50eac_103.conda
  sha256: 0025dd4e6411423903bf478d1b9fbff0cbbbe546f51c9375dfd6729ef2e1a1ac
  md5: 2f7d6347d7acf6edf1ac7f2189f44c8f
  depends:
  - __unix
  - gmpy2 >=2.0.8
  - mpmath >=0.19
  - python * *_cpython
  - python >=3.8
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/sympy
  size: 4256289
  timestamp: 1684180689319
- kind: conda
  name: tbb
  version: 2021.12.0
  build: h00ab1b0_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/tbb-2021.12.0-h00ab1b0_0.conda
  sha256: 0b48f402e18f293e3c7a4c4e391ed2523f173bdec86aa42658db787196eb27ca
  md5: f1b776cff1b426e7e7461a8502a3b731
  depends:
  - libgcc-ng >=12
  - libhwloc >=2.10.0,<2.10.1.0a0
  - libstdcxx-ng >=12
  license: Apache-2.0
  license_family: APACHE
  size: 194192
  timestamp: 1712959702573
- kind: pypi
  name: tenacity
  version: 8.3.0
  url: https://files.pythonhosted.org/packages/61/a1/6bb0cbebefb23641f068bb58a2bc56da9beb2b1c550242e3c540b37698f3/tenacity-8.3.0-py3-none-any.whl
  sha256: 3649f6443dbc0d9b01b9d8020a9c4ec7a1ff5f6f3c6c8a036ef371f573fe9185
  requires_dist:
  - reno ; extra == 'doc'
  - sphinx ; extra == 'doc'
  - pytest ; extra == 'test'
  - tornado>=4.5 ; extra == 'test'
  - typeguard ; extra == 'test'
  requires_python: '>=3.8'
- kind: pypi
  name: tensorboard
  version: 2.16.2
  url: https://files.pythonhosted.org/packages/3a/d0/b97889ffa769e2d1fdebb632084d5e8b53fc299d43a537acee7ec0c021a3/tensorboard-2.16.2-py3-none-any.whl
  sha256: 9f2b4e7dad86667615c0e5cd072f1ea8403fc032a299f0072d6f74855775cc45
  requires_dist:
  - absl-py>=0.4
  - grpcio>=1.48.2
  - markdown>=2.6.8
  - numpy>=1.12.0
  - protobuf!=4.24.0,>=3.19.6
  - setuptools>=41.0.0
  - six>1.9
  - tensorboard-data-server<0.8.0,>=0.7.0
  - werkzeug>=1.0.1
  requires_python: '>=3.9'
- kind: pypi
  name: tensorboard-data-server
  version: 0.7.2
  url: https://files.pythonhosted.org/packages/7a/13/e503968fefabd4c6b2650af21e110aa8466fe21432cd7c43a84577a89438/tensorboard_data_server-0.7.2-py3-none-any.whl
  sha256: 7e0610d205889588983836ec05dc098e80f97b7e7bbff7e994ebb78f578d0ddb
  requires_python: '>=3.7'
- kind: pypi
  name: tensorly
  version: 0.9.0
  url: https://files.pythonhosted.org/packages/c3/ad/83380fb3c85ddf68c509059cf328374c1878f87382070475abc72867c7ba/tensorly-0.9.0-py3-none-any.whl
  sha256: a393022ff1616a36c021496f46a955d94234d6bc8c42793f477a6be8849f4873
  requires_dist:
  - numpy
  - scipy
- kind: pypi
  name: termcolor
  version: 2.4.0
  url: https://files.pythonhosted.org/packages/d9/5f/8c716e47b3a50cbd7c146f45881e11d9414def768b7cd9c5e6650ec2a80a/termcolor-2.4.0-py3-none-any.whl
  sha256: 9297c0df9c99445c2412e832e882a7884038a25617c60cea2ad69488d4040d63
  requires_dist:
  - pytest ; extra == 'tests'
  - pytest-cov ; extra == 'tests'
  requires_python: '>=3.8'
- kind: pypi
  name: terminado
  version: 0.18.1
  url: https://files.pythonhosted.org/packages/6a/9e/2064975477fdc887e47ad42157e214526dcad8f317a948dee17e1659a62f/terminado-0.18.1-py3-none-any.whl
  sha256: a4468e1b37bb318f8a86514f65814e1afc977cf29b3992a4500d9dd305dcceb0
  requires_dist:
  - ptyprocess ; os_name != 'nt'
  - pywinpty>=1.1.0 ; os_name == 'nt'
  - tornado>=6.1.0
  - myst-parser ; extra == 'docs'
  - pydata-sphinx-theme ; extra == 'docs'
  - sphinx ; extra == 'docs'
  - pre-commit ; extra == 'test'
  - pytest-timeout ; extra == 'test'
  - pytest>=7.0 ; extra == 'test'
  - mypy~=1.6 ; extra == 'typing'
  - traitlets>=5.11.1 ; extra == 'typing'
  requires_python: '>=3.8'
- kind: pypi
  name: threadpoolctl
  version: 3.5.0
  url: https://files.pythonhosted.org/packages/4b/2c/ffbf7a134b9ab11a67b0cf0726453cedd9c5043a4fe7a35d1cefa9a1bcfb/threadpoolctl-3.5.0-py3-none-any.whl
  sha256: 56c1e26c150397e58c4926da8eeee87533b1e32bef131bd4bf6a2f45f3185467
  requires_python: '>=3.8'
- kind: pypi
  name: tifffile
  version: 2024.5.3
  url: https://files.pythonhosted.org/packages/c1/cf/dd1cdf85db58c811816377afd6ba8a240f4611e16f4085201598fb2d5578/tifffile-2024.5.3-py3-none-any.whl
  sha256: cac4d939156ff7f16d65fd689637808a7b5b3ad58f9c73327fc009b0aa32c7d5
  requires_dist:
  - numpy
  - imagecodecs>=2023.8.12 ; extra == 'all'
  - matplotlib ; extra == 'all'
  - defusedxml ; extra == 'all'
  - lxml ; extra == 'all'
  - zarr ; extra == 'all'
  - fsspec ; extra == 'all'
  requires_python: '>=3.9'
- kind: pypi
  name: timm
  version: 0.6.7
  url: https://files.pythonhosted.org/packages/72/ed/358a8bc5685c31c0fe7765351b202cf6a8c087893b5d2d64f63c950f8beb/timm-0.6.7-py3-none-any.whl
  sha256: 4bbd7a5c9ae462ec7fec3d99ffc62ac2012010d755248e3de778d50bce5f6186
  requires_dist:
  - torch>=1.4
  - torchvision
  requires_python: '>=3.6'
- kind: pypi
  name: tinycss2
  version: 1.3.0
  url: https://files.pythonhosted.org/packages/2c/4d/0db5b8a613d2a59bbc29bc5bb44a2f8070eb9ceab11c50d477502a8a0092/tinycss2-1.3.0-py3-none-any.whl
  sha256: 54a8dbdffb334d536851be0226030e9505965bb2f30f21a4a82c55fb2a80fae7
  requires_dist:
  - webencodings>=0.4
  - sphinx ; extra == 'doc'
  - sphinx-rtd-theme ; extra == 'doc'
  - pytest ; extra == 'test'
  - ruff ; extra == 'test'
  requires_python: '>=3.8'
- kind: conda
  name: tk
  version: 8.6.13
  build: noxft_h4845f30_101
  build_number: 101
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_h4845f30_101.conda
  sha256: e0569c9caa68bf476bead1bed3d79650bb080b532c64a4af7d8ca286c08dea4e
  md5: d453b98d9c83e71da0741bb0ff4d76bc
  depends:
  - libgcc-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  license: TCL
  license_family: BSD
  size: 3318875
  timestamp: 1699202167581
- kind: pypi
  name: tomli
  version: 2.0.1
  url: https://files.pythonhosted.org/packages/97/75/10a9ebee3fd790d20926a90a2547f0bf78f371b2f13aa822c759680ca7b9/tomli-2.0.1-py3-none-any.whl
  sha256: 939de3e7a6161af0c887ef91b7d41a53e7c5a1ca976325f429cb46ea9bc30ecc
  requires_python: '>=3.7'
- kind: pypi
  name: torch-fidelity
  version: 0.3.0
  url: https://files.pythonhosted.org/packages/9f/2c/e24c7e261eaa00fc911c39a5e30f77efbace480aae2548db9ceaef410945/torch_fidelity-0.3.0-py3-none-any.whl
  sha256: d01284825595feb7dc3eae3dc9a0d8ced02be764813a3483f109bc142b52a1d3
  requires_dist:
  - numpy
  - pillow
  - scipy
  - torch
  - torchvision
  - tqdm
  requires_python: '>=3.6'
- kind: pypi
  name: torchmetrics
  version: 1.4.0
  url: https://files.pythonhosted.org/packages/aa/d9/b235d32de0f496492b108db7aea0cda42c58b713e2547a149385f1dfc8e3/torchmetrics-1.4.0-py3-none-any.whl
  sha256: 18599929a0fff7d4b840a3f9a7700054121850c378caaf7206f4161c0a5dc93c
  requires_dist:
  - numpy>1.20.0
  - packaging>17.1
  - torch>=1.10.0
  - lightning-utilities>=0.8.0
  - pretty-errors==1.2.25
  - typing-extensions ; python_version < '3.9'
  - torchaudio>=0.10.0 ; extra == 'all'
  - pystoi>=0.3.0 ; extra == 'all'
  - pycocotools>2.0.0 ; extra == 'all'
  - torchvision>=0.8 ; extra == 'all'
  - torch-fidelity<=0.4.0 ; extra == 'all'
  - scipy>1.0.0 ; extra == 'all'
  - transformers>=4.10.0 ; extra == 'all'
  - piq<=0.8.0 ; extra == 'all'
  - transformers>4.4.0 ; extra == 'all'
  - sentencepiece>=0.2.0 ; extra == 'all'
  - tqdm>=4.41.0 ; extra == 'all'
  - mecab-python3>=1.0.6 ; extra == 'all'
  - nltk>=3.6 ; extra == 'all'
  - ipadic>=1.0.0 ; extra == 'all'
  - regex>=2021.9.24 ; extra == 'all'
  - types-setuptools ; extra == 'all'
  - types-tabulate ; extra == 'all'
  - types-protobuf ; extra == 'all'
  - mypy==1.9.0 ; extra == 'all'
  - types-emoji ; extra == 'all'
  - torch==2.3.0 ; extra == 'all'
  - types-requests ; extra == 'all'
  - types-six ; extra == 'all'
  - types-pyyaml ; extra == 'all'
  - matplotlib>=3.3.0 ; extra == 'all'
  - scienceplots>=2.0.0 ; extra == 'all'
  - torchaudio>=0.10.0 ; extra == 'audio'
  - pystoi>=0.3.0 ; extra == 'audio'
  - pycocotools>2.0.0 ; extra == 'detection'
  - torchvision>=0.8 ; extra == 'detection'
  - torchaudio>=0.10.0 ; extra == 'dev'
  - pystoi>=0.3.0 ; extra == 'dev'
  - pycocotools>2.0.0 ; extra == 'dev'
  - torchvision>=0.8 ; extra == 'dev'
  - torch-fidelity<=0.4.0 ; extra == 'dev'
  - scipy>1.0.0 ; extra == 'dev'
  - transformers>=4.10.0 ; extra == 'dev'
  - piq<=0.8.0 ; extra == 'dev'
  - transformers>4.4.0 ; extra == 'dev'
  - sentencepiece>=0.2.0 ; extra == 'dev'
  - tqdm>=4.41.0 ; extra == 'dev'
  - mecab-python3>=1.0.6 ; extra == 'dev'
  - nltk>=3.6 ; extra == 'dev'
  - ipadic>=1.0.0 ; extra == 'dev'
  - regex>=2021.9.24 ; extra == 'dev'
  - types-setuptools ; extra == 'dev'
  - types-tabulate ; extra == 'dev'
  - types-protobuf ; extra == 'dev'
  - mypy==1.9.0 ; extra == 'dev'
  - types-emoji ; extra == 'dev'
  - torch==2.3.0 ; extra == 'dev'
  - types-requests ; extra == 'dev'
  - types-six ; extra == 'dev'
  - types-pyyaml ; extra == 'dev'
  - matplotlib>=3.3.0 ; extra == 'dev'
  - scienceplots>=2.0.0 ; extra == 'dev'
  - mir-eval>=0.6 ; extra == 'dev'
  - pytorch-msssim==1.0.0 ; extra == 'dev'
  - sewar>=0.4.4 ; extra == 'dev'
  - mecab-ko-dic>=1.0.0 ; extra == 'dev'
  - huggingface-hub<0.23 ; extra == 'dev'
  - statsmodels>0.13.5 ; extra == 'dev'
  - dython<=0.7.5 ; extra == 'dev'
  - mecab-ko>=1.0.0 ; extra == 'dev'
  - pandas>1.0.0 ; extra == 'dev'
  - numpy<1.25.0 ; extra == 'dev'
  - torch-complex<=0.4.3 ; extra == 'dev'
  - scikit-image>=0.19.0 ; extra == 'dev'
  - pandas>=1.4.0 ; extra == 'dev'
  - netcal>1.0.0 ; extra == 'dev'
  - monai==1.3.0 ; extra == 'dev'
  - jiwer>=2.3.0 ; extra == 'dev'
  - fairlearn ; extra == 'dev'
  - sacrebleu>=2.3.0 ; extra == 'dev'
  - fast-bss-eval>=0.1.0 ; extra == 'dev'
  - kornia>=0.6.7 ; extra == 'dev'
  - bert-score==0.3.13 ; extra == 'dev'
  - faster-coco-eval>=1.3.3 ; extra == 'dev'
  - lpips<=0.1.4 ; extra == 'dev'
  - rouge-score>0.1.0 ; extra == 'dev'
  - torch-fidelity<=0.4.0 ; extra == 'image'
  - torchvision>=0.8 ; extra == 'image'
  - scipy>1.0.0 ; extra == 'image'
  - transformers>=4.10.0 ; extra == 'multimodal'
  - piq<=0.8.0 ; extra == 'multimodal'
  - transformers>4.4.0 ; extra == 'text'
  - sentencepiece>=0.2.0 ; extra == 'text'
  - tqdm>=4.41.0 ; extra == 'text'
  - mecab-python3>=1.0.6 ; extra == 'text'
  - nltk>=3.6 ; extra == 'text'
  - ipadic>=1.0.0 ; extra == 'text'
  - regex>=2021.9.24 ; extra == 'text'
  - types-setuptools ; extra == 'typing'
  - types-tabulate ; extra == 'typing'
  - types-protobuf ; extra == 'typing'
  - mypy==1.9.0 ; extra == 'typing'
  - types-emoji ; extra == 'typing'
  - torch==2.3.0 ; extra == 'typing'
  - types-requests ; extra == 'typing'
  - types-six ; extra == 'typing'
  - types-pyyaml ; extra == 'typing'
  - matplotlib>=3.3.0 ; extra == 'visual'
  - scienceplots>=2.0.0 ; extra == 'visual'
  requires_python: '>=3.8'
- kind: conda
  name: torchtriton
  version: 2.2.0
  build: py310
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/pytorch/linux-64/torchtriton-2.2.0-py310.tar.bz2
  sha256: c997f2d7fcd50320261de5e1f85ae8a6c78ec62cf2a9fea59f0c223ea128cef2
  md5: 3a7c18eb8b99b0cd2f7278b88212f2ab
  depends:
  - filelock
  - python >=3.10,<3.11.0a0
  - pytorch
  license: MIT
  size: 185749974
  timestamp: 1704987377808
- kind: conda
  name: torchvision
  version: 0.17.2
  build: py310_cu118
  subdir: linux-64
  url: https://conda.anaconda.org/pytorch/linux-64/torchvision-0.17.2-py310_cu118.tar.bz2
  sha256: 3eb63c81a09a4f46ea00a8555ead2e154e3cf239993c99699e16fffa970210f4
  md5: d164aac4009b9a7f8d6e201b08994851
  depends:
  - ffmpeg >=4.2
  - libjpeg-turbo
  - libpng
  - numpy >=1.11
  - pillow >=5.3.0,!=8.3.*
  - python >=3.10,<3.11.0a0
  - pytorch 2.2.2
  - pytorch-cuda 11.8.*
  - pytorch-mutex 1.0 cuda
  - requests
  constrains:
  - cpuonly <0
  license: BSD
  purls:
  - pkg:pypi/torchvision
  size: 8555800
  timestamp: 1711423012812
- kind: pypi
  name: tornado
  version: '6.4'
  url: https://files.pythonhosted.org/packages/9f/12/11d0a757bb67278d3380d41955ae98527d5ad18330b2edbdc8de222b569b/tornado-6.4-cp38-abi3-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: f0251554cdd50b4b44362f73ad5ba7126fc5b2c2895cc62b14a1c2d7ea32f212
  requires_python: '>=3.8'
- kind: pypi
  name: tqdm
  version: 4.66.4
  url: https://files.pythonhosted.org/packages/18/eb/fdb7eb9e48b7b02554e1664afd3bd3f117f6b6d6c5881438a0b055554f9b/tqdm-4.66.4-py3-none-any.whl
  sha256: b75ca56b413b030bc3f00af51fd2c1a1a5eac6a0c1cca83cbb37a5c52abce644
  requires_dist:
  - colorama ; platform_system == 'Windows'
  - pytest>=6 ; extra == 'dev'
  - pytest-cov ; extra == 'dev'
  - pytest-timeout ; extra == 'dev'
  - pytest-xdist ; extra == 'dev'
  - ipywidgets>=6 ; extra == 'notebook'
  - slack-sdk ; extra == 'slack'
  - requests ; extra == 'telegram'
  requires_python: '>=3.7'
- kind: pypi
  name: traitlets
  version: 5.14.3
  url: https://files.pythonhosted.org/packages/00/c0/8f5d070730d7836adc9c9b6408dec68c6ced86b304a9b26a14df072a6e8c/traitlets-5.14.3-py3-none-any.whl
  sha256: b74e89e397b1ed28cc831db7aea759ba6640cb3de13090ca145426688ff1ac4f
  requires_dist:
  - myst-parser ; extra == 'docs'
  - pydata-sphinx-theme ; extra == 'docs'
  - sphinx ; extra == 'docs'
  - argcomplete>=3.0.3 ; extra == 'test'
  - mypy>=1.7.0 ; extra == 'test'
  - pre-commit ; extra == 'test'
  - pytest-mock ; extra == 'test'
  - pytest-mypy-testing ; extra == 'test'
  - pytest<8.2,>=7.0 ; extra == 'test'
  requires_python: '>=3.8'
- kind: pypi
  name: trimesh
  version: 4.3.2
  url: https://files.pythonhosted.org/packages/57/7b/557a411622ff3e144d52bebbf8ad8a28bb18ee55ff68e1eb5ebfce755975/trimesh-4.3.2-py3-none-any.whl
  sha256: 7563182a9379485b88a44e87156fe54b41fb6f8f030001b9b6de39abdef05c22
  requires_dist:
  - numpy>=1.20
  - trimesh[easy,recommend,test] ; extra == 'all'
  - colorlog ; extra == 'easy'
  - mapbox-earcut ; extra == 'easy'
  - chardet ; extra == 'easy'
  - lxml ; extra == 'easy'
  - jsonschema ; extra == 'easy'
  - networkx ; extra == 'easy'
  - svg-path ; extra == 'easy'
  - pycollada ; extra == 'easy'
  - setuptools ; extra == 'easy'
  - shapely ; extra == 'easy'
  - xxhash ; extra == 'easy'
  - rtree ; extra == 'easy'
  - httpx ; extra == 'easy'
  - scipy ; extra == 'easy'
  - embreex ; extra == 'easy'
  - pillow ; extra == 'easy'
  - vhacdx ; extra == 'easy'
  - xatlas ; extra == 'easy'
  - glooey ; extra == 'recommend'
  - sympy ; extra == 'recommend'
  - meshio ; extra == 'recommend'
  - pyglet<2 ; extra == 'recommend'
  - psutil ; extra == 'recommend'
  - scikit-image ; extra == 'recommend'
  - python-fcl ; extra == 'recommend'
  - openctm ; extra == 'recommend'
  - cascadio ; extra == 'recommend'
  - manifold3d>=2.3.0 ; extra == 'recommend'
  - pytest-cov ; extra == 'test'
  - coveralls ; extra == 'test'
  - pyright ; extra == 'test'
  - ezdxf ; extra == 'test'
  - gmsh>=4.12.1 ; extra == 'test'
  - pytest ; extra == 'test'
  - pymeshlab ; extra == 'test'
  - pyinstrument ; extra == 'test'
  - matplotlib ; extra == 'test'
  - ruff ; extra == 'test'
  - pytest-beartype ; python_version >= '3.10' and extra == 'test'
  requires_python: '>=3.7'
- kind: pypi
  name: typeguard
  version: 4.4.2
  url: https://files.pythonhosted.org/packages/cf/4b/9a77dc721aa0b7f74440a42e4ef6f9a4fae7324e17f64f88b96f4c25cc05/typeguard-4.4.2-py3-none-any.whl
  sha256: 77a78f11f09777aeae7fa08585f33b5f4ef0e7335af40005b0c422ed398ff48c
  requires_dist:
  - importlib-metadata>=3.6 ; python_version < '3.10'
  - typing-extensions>=4.10.0
  - coverage[toml]>=7 ; extra == 'test'
  - pytest>=7 ; extra == 'test'
  - mypy>=1.2.0 ; platform_python_implementation != 'PyPy' and extra == 'test'
  - packaging ; extra == 'doc'
  - sphinx>=7 ; extra == 'doc'
  - sphinx-autodoc-typehints>=1.2.0 ; extra == 'doc'
  - sphinx-rtd-theme>=1.3.0 ; extra == 'doc'
  requires_python: '>=3.9'
- kind: pypi
  name: types-python-dateutil
  version: 2.9.0.20240316
  url: https://files.pythonhosted.org/packages/c7/1b/af4f4c4f3f7339a4b7eb3c0ab13416db98f8ac09de3399129ee5fdfa282b/types_python_dateutil-2.9.0.20240316-py3-none-any.whl
  sha256: 6b8cb66d960771ce5ff974e9dd45e38facb81718cc1e208b10b1baccbfdbee3b
  requires_python: '>=3.8'
- kind: conda
  name: typing_extensions
  version: 4.11.0
  build: pyha770c72_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.11.0-pyha770c72_0.conda
  sha256: a7e8714d14f854058e971a6ed44f18cc37cc685f98ddefb2e6b7899a0cc4d1a2
  md5: 6ef2fc37559256cf682d8b3375e89b80
  depends:
  - python >=3.8
  license: PSF-2.0
  license_family: PSF
  purls:
  - pkg:pypi/typing-extensions
  size: 37583
  timestamp: 1712330089194
- kind: pypi
  name: tyro
  version: 0.9.17
  url: https://files.pythonhosted.org/packages/4d/b1/c0c62adcd5e64244af876a3173f194d02dc1fe830cae28370b6d9e315971/tyro-0.9.17-py3-none-any.whl
  sha256: f601fcd27edc99bc10a03602a47930ce8906ed995179a98b689bc85db38bec40
  requires_dist:
  - backports-cached-property>=1.0.2 ; python_version < '3.8'
  - colorama>=0.4.0 ; platform_system == 'Windows'
  - docstring-parser>=0.15
  - eval-type-backport>=0.1.3 ; python_version < '3.10'
  - rich>=11.1.0
  - shtab>=1.5.6
  - typeguard>=4.0.0
  - typing-extensions>=4.7.0 ; python_version < '3.8'
  - typing-extensions>=4.9.0 ; python_version >= '3.8'
  - attrs>=21.4.0 ; extra == 'dev'
  - coverage[toml]>=6.5.0 ; extra == 'dev'
  - eval-type-backport>=0.1.3 ; extra == 'dev'
  - flax>=0.6.9 ; (python_version >= '3.8' and python_version <= '3.12') and extra == 'dev'
  - ml-collections>=0.1.0 ; extra == 'dev'
  - mypy>=1.4.1 ; extra == 'dev'
  - numpy>=1.20.0 ; extra == 'dev'
  - omegaconf>=2.2.2 ; extra == 'dev'
  - pydantic!=2.10.0,>=2.5.2 ; extra == 'dev'
  - pyright!=1.1.379,>=1.1.349 ; extra == 'dev'
  - pytest-cov>=3.0.0 ; extra == 'dev'
  - pytest-xdist>=3.5.0 ; extra == 'dev'
  - pytest>=7.1.2 ; extra == 'dev'
  - pyyaml>=6.0 ; extra == 'dev'
  - ruff>=0.1.13 ; extra == 'dev'
  - torch>=1.10.0 ; python_version <= '3.12' and extra == 'dev'
  requires_python: '>=3.7'
- kind: pypi
  name: tzdata
  version: '2025.1'
  url: https://files.pythonhosted.org/packages/0f/dd/84f10e23edd882c6f968c21c2434fe67bd4a528967067515feca9e611e5e/tzdata-2025.1-py2.py3-none-any.whl
  sha256: 7e127113816800496f027041c570f50bcd464a020098a3b6b199517772303639
  requires_python: '>=2'
- kind: conda
  name: tzdata
  version: 2024a
  build: h0c530f3_0
  subdir: noarch
  noarch: generic
  url: https://conda.anaconda.org/conda-forge/noarch/tzdata-2024a-h0c530f3_0.conda
  sha256: 7b2b69c54ec62a243eb6fba2391b5e443421608c3ae5dbff938ad33ca8db5122
  md5: 161081fc7cec0bfda0d86d7cb595f8d8
  license: LicenseRef-Public-Domain
  size: 119815
  timestamp: 1706886945727
- kind: conda
  name: ucx
  version: 1.15.0
  build: ha691c75_8
  build_number: 8
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/ucx-1.15.0-ha691c75_8.conda
  sha256: 85b40ac6607c9e4e32bcb13e95da41ff48a10f813df0c1e74ff32412e1f7da35
  md5: 3f9bc6137b240642504a6c9b07a10c25
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - rdma-core >=51.0
  constrains:
  - cuda-version >=11.2,<12
  license: BSD-3-Clause
  license_family: BSD
  size: 6842006
  timestamp: 1712025621683
- kind: pypi
  name: uri-template
  version: 1.3.0
  url: https://files.pythonhosted.org/packages/e7/00/3fca040d7cf8a32776d3d81a00c8ee7457e00f80c649f1e4a863c8321ae9/uri_template-1.3.0-py3-none-any.whl
  sha256: a44a133ea12d44a0c0f06d7d42a52d71282e77e2f937d8abd5655b8d56fc1363
  requires_dist:
  - types-pyyaml ; extra == 'dev'
  - mypy ; extra == 'dev'
  - flake8 ; extra == 'dev'
  - flake8-annotations ; extra == 'dev'
  - flake8-bandit ; extra == 'dev'
  - flake8-bugbear ; extra == 'dev'
  - flake8-commas ; extra == 'dev'
  - flake8-comprehensions ; extra == 'dev'
  - flake8-continuation ; extra == 'dev'
  - flake8-datetimez ; extra == 'dev'
  - flake8-docstrings ; extra == 'dev'
  - flake8-import-order ; extra == 'dev'
  - flake8-literal ; extra == 'dev'
  - flake8-modern-annotations ; extra == 'dev'
  - flake8-noqa ; extra == 'dev'
  - flake8-pyproject ; extra == 'dev'
  - flake8-requirements ; extra == 'dev'
  - flake8-typechecking-import ; extra == 'dev'
  - flake8-use-fstring ; extra == 'dev'
  - pep8-naming ; extra == 'dev'
  requires_python: '>=3.7'
- kind: conda
  name: urllib3
  version: 2.2.1
  build: pyhd8ed1ab_0
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.2.1-pyhd8ed1ab_0.conda
  sha256: d4009dcc9327684d6409706ce17656afbeae690d8522d3c9bc4df57649a352cd
  md5: 08807a87fa7af10754d46f63b368e016
  depends:
  - brotli-python >=1.0.9
  - pysocks >=1.5.6,<2.0,!=1.5.7
  - python >=3.7
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/urllib3
  size: 94669
  timestamp: 1708239595549
- kind: pypi
  name: vhacdx
  version: 0.0.6
  url: https://files.pythonhosted.org/packages/7e/af/d3af73e45f8f2d1f3134df23f3832f2b248efadbc4a75d2de2fced454a20/vhacdx-0.0.6-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: e2c992c36a0149fba8c3581ec7cb6711e9803c48357cea75b08c97c688dece21
  requires_dist:
  - numpy
  - pytest ; extra == 'test'
  requires_python: '>=3.7'
- kind: pypi
  name: viser
  version: 0.2.7
  url: https://files.pythonhosted.org/packages/1b/fe/04034c0fb25f7b823b331c20da35f6f688cadbc57c00f1e0591d88ba6caf/viser-0.2.7-py3-none-any.whl
  sha256: f878846c61f066e69645fdd39a443ed307d3bc77a25b34ed845158e7ffeda32b
  requires_dist:
  - websockets>=10.4
  - numpy>=1.0.0
  - msgspec>=0.18.6
  - imageio>=2.0.0
  - scikit-image>=0.18.0
  - scipy>=1.7.3
  - tqdm>=4.0.0
  - tyro>=0.2.0
  - rich>=13.3.3
  - trimesh>=3.21.7
  - nodeenv>=1.8.0
  - psutil>=5.9.5
  - yourdfpy>=0.0.53
  - plyfile>=1.0.2
  - pyliblzfse>=0.4.1 ; platform_system != 'Windows'
  - pyright>=1.1.308 ; extra == 'dev'
  - ruff==0.6.2 ; extra == 'dev'
  - pre-commit==3.3.2 ; extra == 'dev'
  - torch>=1.13.1 ; extra == 'examples'
  - matplotlib>=3.7.1 ; extra == 'examples'
  - plotly>=5.21.0 ; extra == 'examples'
  - robot-descriptions>=1.10.0 ; extra == 'examples'
  - gdown>=4.6.6 ; extra == 'examples'
  - plyfile ; extra == 'examples'
  requires_python: '>=3.8'
- kind: conda
  name: vlfeat
  version: 0.9.21
  build: hd590300_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/vlfeat-0.9.21-hd590300_1.conda
  sha256: f3fca24ef6a327072378dd654eba37c6a6da15499ca212eb20b0af422c1ad4c5
  md5: d0749e345cdf00cb4f8f137fd652fefa
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 236419
  timestamp: 1684580133228
- kind: pypi
  name: wadler-lindig
  version: 0.1.4
  url: https://files.pythonhosted.org/packages/7b/69/cfb1af44622044d4db0cad65721d283a921a4795f0ad121616b9eaa6ccd7/wadler_lindig-0.1.4-py3-none-any.whl
  sha256: 5c463aeb1f4ddc4acc12c3708d22ae21bcfc3e19e7c4d7aeef6642ea57b1a8b8
  requires_dist:
  - numpy ; extra == 'dev'
  - pre-commit ; extra == 'dev'
  - pytest ; extra == 'dev'
  - hippogriffe==0.1.0 ; extra == 'docs'
  - mkdocs-include-exclude-files==0.1.0 ; extra == 'docs'
  - mkdocs-ipynb==0.1.0 ; extra == 'docs'
  - mkdocs-material==9.6.7 ; extra == 'docs'
  - mkdocs==1.6.1 ; extra == 'docs'
  - mkdocstrings[python]==0.28.3 ; extra == 'docs'
  - pymdown-extensions==10.14.3 ; extra == 'docs'
  requires_python: '>=3.10'
- kind: pypi
  name: wandb
  version: 0.17.0
  url: https://files.pythonhosted.org/packages/b6/49/fd266df341165c7ed94f0e9875bb20cc75a59b1dd9b6f6a51a31db5f8527/wandb-0.17.0-py3-none-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: 56a1dd6e0e635cba3f6ed30b52c71739bdc2a3e57df155619d2d80ee952b4201
  requires_dist:
  - click!=8.0.0,>=7.1
  - docker-pycreds>=0.4.0
  - gitpython!=3.1.29,>=1.0.0
  - platformdirs
  - protobuf!=4.21.0,<5,>=3.12.0 ; python_version < '3.9' and sys_platform == 'linux'
  - protobuf!=4.21.0,<5,>=3.15.0 ; python_version == '3.9' and sys_platform == 'linux'
  - protobuf!=4.21.0,<5,>=3.19.0 ; python_version > '3.9' and sys_platform == 'linux'
  - protobuf!=4.21.0,<5,>=3.19.0 ; sys_platform != 'linux'
  - psutil>=5.0.0
  - pyyaml
  - requests<3,>=2.0.0
  - sentry-sdk>=1.0.0
  - setproctitle
  - setuptools
  - typing-extensions ; python_version < '3.10'
  - boto3 ; extra == 'aws'
  - azure-identity ; extra == 'azure'
  - azure-storage-blob ; extra == 'azure'
  - google-cloud-storage ; extra == 'gcp'
  - filelock ; extra == 'importers'
  - mlflow ; extra == 'importers'
  - polars ; extra == 'importers'
  - rich ; extra == 'importers'
  - tenacity ; extra == 'importers'
  - google-cloud-storage ; extra == 'kubeflow'
  - kubernetes ; extra == 'kubeflow'
  - minio ; extra == 'kubeflow'
  - sh ; extra == 'kubeflow'
  - awscli ; extra == 'launch'
  - azure-containerregistry ; extra == 'launch'
  - azure-identity ; extra == 'launch'
  - azure-storage-blob ; extra == 'launch'
  - boto3 ; extra == 'launch'
  - botocore ; extra == 'launch'
  - chardet ; extra == 'launch'
  - google-auth ; extra == 'launch'
  - google-cloud-aiplatform ; extra == 'launch'
  - google-cloud-artifact-registry ; extra == 'launch'
  - google-cloud-compute ; extra == 'launch'
  - google-cloud-storage ; extra == 'launch'
  - iso8601 ; extra == 'launch'
  - kubernetes ; extra == 'launch'
  - kubernetes-asyncio ; extra == 'launch'
  - nbconvert ; extra == 'launch'
  - nbformat ; extra == 'launch'
  - optuna ; extra == 'launch'
  - pydantic ; extra == 'launch'
  - pyyaml>=6.0.0 ; extra == 'launch'
  - tomli ; extra == 'launch'
  - typing-extensions ; extra == 'launch'
  - bokeh ; extra == 'media'
  - moviepy ; extra == 'media'
  - numpy ; extra == 'media'
  - pillow ; extra == 'media'
  - plotly>=5.18.0 ; extra == 'media'
  - rdkit-pypi ; extra == 'media'
  - soundfile ; extra == 'media'
  - cloudpickle ; extra == 'models'
  - orjson ; extra == 'perf'
  - pydantic>=2.0.0 ; extra == 'reports'
  - sweeps>=0.2.0 ; extra == 'sweeps'
  requires_python: '>=3.7'
- kind: pypi
  name: wcwidth
  version: 0.2.13
  url: https://files.pythonhosted.org/packages/fd/84/fd2ba7aafacbad3c4201d395674fc6348826569da3c0937e75505ead3528/wcwidth-0.2.13-py2.py3-none-any.whl
  sha256: 3da69048e4540d84af32131829ff948f1e022c1c6bdb8d6102117aac784f6859
  requires_dist:
  - backports-functools-lru-cache>=1.2.1 ; python_version < '3.2'
- kind: pypi
  name: webcolors
  version: '1.13'
  url: https://files.pythonhosted.org/packages/d5/e1/3e9013159b4cbb71df9bd7611cbf90dc2c621c8aeeb677fc41dad72f2261/webcolors-1.13-py3-none-any.whl
  sha256: 29bc7e8752c0a1bd4a1f03c14d6e6a72e93d82193738fa860cbff59d0fcc11bf
  requires_dist:
  - furo ; extra == 'docs'
  - sphinx ; extra == 'docs'
  - sphinx-copybutton ; extra == 'docs'
  - sphinx-inline-tabs ; extra == 'docs'
  - sphinx-notfound-page ; extra == 'docs'
  - sphinxext-opengraph ; extra == 'docs'
  - pytest ; extra == 'tests'
  - pytest-cov ; extra == 'tests'
  requires_python: '>=3.7'
- kind: pypi
  name: webencodings
  version: 0.5.1
  url: https://files.pythonhosted.org/packages/f4/24/2a3e3df732393fed8b3ebf2ec078f05546de641fe1b667ee316ec1dcf3b7/webencodings-0.5.1-py2.py3-none-any.whl
  sha256: a0af1213f3c2226497a97e2b3aa01a7e4bee4f403f95be16fc9acd2947514a78
- kind: pypi
  name: websocket-client
  version: 1.3.3
  url: https://files.pythonhosted.org/packages/67/b4/91683d7d5f66393e8877492fe4763304f82dbe308658a8db98f7a9e20baf/websocket_client-1.3.3-py3-none-any.whl
  sha256: 5d55652dc1d0b3c734f044337d929aaf83f4f9138816ec680c1aefefb4dc4877
  requires_dist:
  - sphinx>=3.4 ; extra == 'docs'
  - sphinx-rtd-theme>=0.5 ; extra == 'docs'
  - python-socks ; extra == 'optional'
  - wsaccel ; extra == 'optional'
  - websockets ; extra == 'test'
  requires_python: '>=3.7'
- kind: pypi
  name: websockets
  version: '12.0'
  url: https://files.pythonhosted.org/packages/9a/12/c7a7504f5bf74d6ee0533f6fc7d30d8f4b79420ab179d1df2484b07602eb/websockets-12.0-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: 6350b14a40c95ddd53e775dbdbbbc59b124a5c8ecd6fbb09c2e52029f7a9f480
  requires_python: '>=3.8'
- kind: pypi
  name: werkzeug
  version: 3.0.3
  url: https://files.pythonhosted.org/packages/9d/6e/e792999e816d19d7fcbfa94c730936750036d65656a76a5a688b57a656c4/werkzeug-3.0.3-py3-none-any.whl
  sha256: fc9645dc43e03e4d630d23143a04a7f947a9a3b5727cd535fdfe155a17cc48c8
  requires_dist:
  - markupsafe>=2.1.1
  - watchdog>=2.3 ; extra == 'watchdog'
  requires_python: '>=3.8'
- kind: conda
  name: wheel
  version: 0.43.0
  build: pyhd8ed1ab_1
  build_number: 1
  subdir: noarch
  noarch: python
  url: https://conda.anaconda.org/conda-forge/noarch/wheel-0.43.0-pyhd8ed1ab_1.conda
  sha256: cb318f066afd6fd64619f14c030569faf3f53e6f50abf743b4c865e7d95b96bc
  md5: 0b5293a157c2b5cd513dd1b03d8d3aae
  depends:
  - python >=3.8
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/wheel
  size: 57963
  timestamp: 1711546009410
- kind: pypi
  name: widgetsnbextension
  version: 4.0.10
  url: https://files.pythonhosted.org/packages/99/bc/82a8c3985209ca7c0a61b383c80e015fd92e74f8ba0ec1af98f9d6ca8dce/widgetsnbextension-4.0.10-py3-none-any.whl
  sha256: d37c3724ec32d8c48400a435ecfa7d3e259995201fbefa37163124a9fcb393cc
  requires_python: '>=3.7'
- kind: pypi
  name: wrapt
  version: 1.16.0
  url: https://files.pythonhosted.org/packages/49/83/b40bc1ad04a868b5b5bcec86349f06c1ee1ea7afe51dc3e46131e4f39308/wrapt-1.16.0-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: ac83a914ebaf589b69f7d0a1277602ff494e21f4c2f743313414378f8f50a4cf
  requires_python: '>=3.6'
- kind: pypi
  name: wsproto
  version: 1.2.0
  url: https://files.pythonhosted.org/packages/78/58/e860788190eba3bcce367f74d29c4675466ce8dddfba85f7827588416f01/wsproto-1.2.0-py3-none-any.whl
  sha256: b9acddd652b585d75b20477888c56642fdade28bdfd3579aa24a4d2c037dd736
  requires_dist:
  - h11<1,>=0.9.0
  requires_python: '>=3.7.0'
- kind: pypi
  name: wurlitzer
  version: 3.1.0
  url: https://files.pythonhosted.org/packages/51/23/33f2ed4247e30770d964e3952bf4cbe13d22846eca940557447e0a4a2125/wurlitzer-3.1.0-py3-none-any.whl
  sha256: a20994e53681fea8fb9d16dc05ff5f9f9701f09723d894456a6d6b29cfb3188d
  requires_python: '>=3.5'
- kind: conda
  name: x264
  version: 1!164.3095
  build: h166bdaf_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/x264-1!164.3095-h166bdaf_2.tar.bz2
  sha256: 175315eb3d6ea1f64a6ce470be00fa2ee59980108f246d3072ab8b977cb048a5
  md5: 6c99772d483f566d59e25037fea2c4b1
  depends:
  - libgcc-ng >=12
  license: GPL-2.0-or-later
  license_family: GPL
  size: 897548
  timestamp: 1660323080555
- kind: conda
  name: x265
  version: '3.5'
  build: h924138e_3
  build_number: 3
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/x265-3.5-h924138e_3.tar.bz2
  sha256: 76c7405bcf2af639971150f342550484efac18219c0203c5ee2e38b8956fe2a0
  md5: e7f6ed84d4623d52ee581325c1587a6b
  depends:
  - libgcc-ng >=10.3.0
  - libstdcxx-ng >=10.3.0
  license: GPL-2.0-or-later
  license_family: GPL
  size: 3357188
  timestamp: 1646609687141
- kind: pypi
  name: xatlas
  version: 0.0.9
  url: https://files.pythonhosted.org/packages/29/ee/086c7cb4f067238e55fd645188232d67afe1ac6adacbea7a8cf2c6346cd8/xatlas-0.0.9-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: 7112a4c90c14a459dfffbffac1319ad3770a50bfb592f03562c8fe564e765ad7
  requires_dist:
  - numpy
  - trimesh ; extra == 'test'
  - pytest ; extra == 'test'
  requires_python: '>=3.7'
- kind: conda
  name: xcb-util
  version: 0.4.0
  build: hd590300_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-0.4.0-hd590300_1.conda
  sha256: 0c91d87f0efdaadd4e56a5f024f8aab20ec30f90aa2ce9e4ebea05fbc20f71ad
  md5: 9bfac7ccd94d54fd21a0501296d60424
  depends:
  - libgcc-ng >=12
  - libxcb >=1.13
  - libxcb >=1.15,<1.16.0a0
  license: MIT
  license_family: MIT
  size: 19728
  timestamp: 1684639166048
- kind: conda
  name: xcb-util-image
  version: 0.4.0
  build: h8ee46fc_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-image-0.4.0-h8ee46fc_1.conda
  sha256: 92ffd68d2801dbc27afe223e04ae7e78ef605fc8575f107113c93c7bafbd15b0
  md5: 9d7bcddf49cbf727730af10e71022c73
  depends:
  - libgcc-ng >=12
  - libxcb >=1.15,<1.16.0a0
  - xcb-util >=0.4.0,<0.5.0a0
  license: MIT
  license_family: MIT
  size: 24474
  timestamp: 1684679894554
- kind: conda
  name: xcb-util-keysyms
  version: 0.4.0
  build: h8ee46fc_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-keysyms-0.4.0-h8ee46fc_1.conda
  sha256: 8451d92f25d6054a941b962179180728c48c62aab5bf20ac10fef713d5da6a9a
  md5: 632413adcd8bc16b515cab87a2932913
  depends:
  - libgcc-ng >=12
  - libxcb >=1.15,<1.16.0a0
  license: MIT
  license_family: MIT
  size: 14186
  timestamp: 1684680497805
- kind: conda
  name: xcb-util-renderutil
  version: 0.3.9
  build: hd590300_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-renderutil-0.3.9-hd590300_1.conda
  sha256: 6987588e6fff5892056021c2ea52f7a0deefb2c7348e70d24750e2d60dabf009
  md5: e995b155d938b6779da6ace6c6b13816
  depends:
  - libgcc-ng >=12
  - libxcb >=1.13
  - libxcb >=1.15,<1.16.0a0
  license: MIT
  license_family: MIT
  size: 16955
  timestamp: 1684639112393
- kind: conda
  name: xcb-util-wm
  version: 0.4.1
  build: h8ee46fc_1
  build_number: 1
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-wm-0.4.1-h8ee46fc_1.conda
  sha256: 08ba7147c7579249b6efd33397dc1a8c2404278053165aaecd39280fee705724
  md5: 90108a432fb5c6150ccfee3f03388656
  depends:
  - libgcc-ng >=12
  - libxcb >=1.15,<1.16.0a0
  license: MIT
  license_family: MIT
  size: 52114
  timestamp: 1684679248466
- kind: conda
  name: xkeyboard-config
  version: '2.41'
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xkeyboard-config-2.41-hd590300_0.conda
  sha256: 56955610c0747ea7cb026bb8aa9ef165ff41d616e89894538173b8b7dd2ee49a
  md5: 81f740407b45e3f9047b3174fa94eb9e
  depends:
  - libgcc-ng >=12
  - xorg-libx11 >=1.8.7,<2.0a0
  license: MIT
  license_family: MIT
  size: 898045
  timestamp: 1707104384997
- kind: conda
  name: xorg-fixesproto
  version: '5.0'
  build: h7f98852_1002
  build_number: 1002
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-fixesproto-5.0-h7f98852_1002.tar.bz2
  sha256: 5d2af1b40f82128221bace9466565eca87c97726bb80bbfcd03871813f3e1876
  md5: 65ad6e1eb4aed2b0611855aff05e04f6
  depends:
  - libgcc-ng >=9.3.0
  - xorg-xextproto
  license: MIT
  license_family: MIT
  size: 9122
  timestamp: 1617479697350
- kind: conda
  name: xorg-kbproto
  version: 1.0.7
  build: h7f98852_1002
  build_number: 1002
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-kbproto-1.0.7-h7f98852_1002.tar.bz2
  sha256: e90b0a6a5d41776f11add74aa030f789faf4efd3875c31964d6f9cfa63a10dd1
  md5: 4b230e8381279d76131116660f5a241a
  depends:
  - libgcc-ng >=9.3.0
  license: MIT
  license_family: MIT
  size: 27338
  timestamp: 1610027759842
- kind: conda
  name: xorg-libice
  version: 1.1.1
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-libice-1.1.1-hd590300_0.conda
  sha256: 5aa9b3682285bb2bf1a8adc064cb63aff76ef9178769740d855abb42b0d24236
  md5: b462a33c0be1421532f28bfe8f4a7514
  depends:
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 58469
  timestamp: 1685307573114
- kind: conda
  name: xorg-libsm
  version: 1.2.4
  build: h7391055_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-libsm-1.2.4-h7391055_0.conda
  sha256: 089ad5f0453c604e18985480218a84b27009e9e6de9a0fa5f4a20b8778ede1f1
  md5: 93ee23f12bc2e684548181256edd2cf6
  depends:
  - libgcc-ng >=12
  - libuuid >=2.38.1,<3.0a0
  - xorg-libice >=1.1.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 27433
  timestamp: 1685453649160
- kind: conda
  name: xorg-libx11
  version: 1.8.9
  build: h8ee46fc_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.9-h8ee46fc_0.conda
  sha256: 3e53ba247f1ad68353f18aceba5bf8ce87e3dea930de85d36946844a7658c9fb
  md5: 077b6e8ad6a3ddb741fce2496dd01bec
  depends:
  - libgcc-ng >=12
  - libxcb >=1.15,<1.16.0a0
  - xorg-kbproto
  - xorg-xextproto >=7.3.0,<8.0a0
  - xorg-xproto
  license: MIT
  license_family: MIT
  size: 828060
  timestamp: 1712415742569
- kind: conda
  name: xorg-libxau
  version: 1.0.11
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.11-hd590300_0.conda
  sha256: 309751371d525ce50af7c87811b435c176915239fc9e132b99a25d5e1703f2d4
  md5: 2c80dc38fface310c9bd81b17037fee5
  depends:
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 14468
  timestamp: 1684637984591
- kind: conda
  name: xorg-libxdmcp
  version: 1.1.3
  build: h7f98852_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.3-h7f98852_0.tar.bz2
  sha256: 4df7c5ee11b8686d3453e7f3f4aa20ceef441262b49860733066c52cfd0e4a77
  md5: be93aabceefa2fac576e971aef407908
  depends:
  - libgcc-ng >=9.3.0
  license: MIT
  license_family: MIT
  size: 19126
  timestamp: 1610071769228
- kind: conda
  name: xorg-libxext
  version: 1.3.4
  build: h0b41bf4_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxext-1.3.4-h0b41bf4_2.conda
  sha256: 73e5cfbdff41ef8a844441f884412aa5a585a0f0632ec901da035a03e1fe1249
  md5: 82b6df12252e6f32402b96dacc656fec
  depends:
  - libgcc-ng >=12
  - xorg-libx11 >=1.7.2,<2.0a0
  - xorg-xextproto
  license: MIT
  license_family: MIT
  size: 50143
  timestamp: 1677036907815
- kind: conda
  name: xorg-libxfixes
  version: 5.0.3
  build: h7f98852_1004
  build_number: 1004
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxfixes-5.0.3-h7f98852_1004.tar.bz2
  sha256: 1e426a1abb774ef1dcf741945ed5c42ad12ea2dc7aeed7682d293879c3e1e4c3
  md5: e9a21aa4d5e3e5f1aed71e8cefd46b6a
  depends:
  - libgcc-ng >=9.3.0
  - xorg-fixesproto
  - xorg-libx11 >=1.7.0,<2.0a0
  license: MIT
  license_family: MIT
  size: 18145
  timestamp: 1617717802636
- kind: conda
  name: xorg-libxrender
  version: 0.9.11
  build: hd590300_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrender-0.9.11-hd590300_0.conda
  sha256: 26da4d1911473c965c32ce2b4ff7572349719eaacb88a066db8d968a4132c3f7
  md5: ed67c36f215b310412b2af935bf3e530
  depends:
  - libgcc-ng >=12
  - xorg-libx11 >=1.8.6,<2.0a0
  - xorg-renderproto
  license: MIT
  license_family: MIT
  size: 37770
  timestamp: 1688300707994
- kind: conda
  name: xorg-renderproto
  version: 0.11.1
  build: h7f98852_1002
  build_number: 1002
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-renderproto-0.11.1-h7f98852_1002.tar.bz2
  sha256: 38942930f233d1898594dd9edf4b0c0786f3dbc12065a0c308634c37fd936034
  md5: 06feff3d2634e3097ce2fe681474b534
  depends:
  - libgcc-ng >=9.3.0
  license: MIT
  license_family: MIT
  size: 9621
  timestamp: 1614866326326
- kind: conda
  name: xorg-xextproto
  version: 7.3.0
  build: h0b41bf4_1003
  build_number: 1003
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-xextproto-7.3.0-h0b41bf4_1003.conda
  sha256: b8dda3b560e8a7830fe23be1c58cc41f407b2e20ae2f3b6901eb5842ba62b743
  md5: bce9f945da8ad2ae9b1d7165a64d0f87
  depends:
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  size: 30270
  timestamp: 1677036833037
- kind: conda
  name: xorg-xf86vidmodeproto
  version: 2.3.1
  build: h7f98852_1002
  build_number: 1002
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-xf86vidmodeproto-2.3.1-h7f98852_1002.tar.bz2
  sha256: 43398aeacad5b8753b7a1c12cb6bca36124e0c842330372635879c350c430791
  md5: 3ceea9668625c18f19530de98b15d5b0
  depends:
  - libgcc-ng >=9.3.0
  license: MIT
  license_family: MIT
  size: 23875
  timestamp: 1620067286978
- kind: conda
  name: xorg-xproto
  version: 7.0.31
  build: h7f98852_1007
  build_number: 1007
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xorg-xproto-7.0.31-h7f98852_1007.tar.bz2
  sha256: f197bb742a17c78234c24605ad1fe2d88b1d25f332b75d73e5ba8cf8fbc2a10d
  md5: b4a4381d54784606820704f7b5f05a15
  depends:
  - libgcc-ng >=9.3.0
  license: MIT
  license_family: MIT
  size: 74922
  timestamp: 1607291557628
- kind: pypi
  name: xxhash
  version: 3.4.1
  url: https://files.pythonhosted.org/packages/80/8a/1dd41557883b6196f8f092011a5c1f72d4d44cf36d7b67d4a5efe3127949/xxhash-3.4.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  sha256: 00f2fdef6b41c9db3d2fc0e7f94cb3db86693e5c45d6de09625caad9a469635b
  requires_python: '>=3.7'
- kind: conda
  name: xz
  version: 5.2.6
  build: h166bdaf_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/xz-5.2.6-h166bdaf_0.tar.bz2
  sha256: 03a6d28ded42af8a347345f82f3eebdd6807a08526d47899a42d62d319609162
  md5: 2161070d867d1b1204ea749c8eec4ef0
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1 and GPL-2.0
  size: 418368
  timestamp: 1660346797927
- kind: conda
  name: yaml
  version: 0.2.5
  build: h7f98852_2
  build_number: 2
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/yaml-0.2.5-h7f98852_2.tar.bz2
  sha256: a4e34c710eeb26945bdbdaba82d3d74f60a78f54a874ec10d373811a5d217535
  md5: 4cb3ad778ec2d5a7acbdf254eb1c42ae
  depends:
  - libgcc-ng >=9.4.0
  license: MIT
  license_family: MIT
  size: 89141
  timestamp: 1641346969816
- kind: pypi
  name: yourdfpy
  version: 0.0.56
  url: https://files.pythonhosted.org/packages/a5/a3/b182c56518f208e3b10a979a24dbe7348fc39edf19e6271b6cf5f8988c96/yourdfpy-0.0.56-py3-none-any.whl
  sha256: 85063021aafa97e3818c9e708bc67a804678bbd7f8bbd3c4425f243ff58f9499
  requires_dist:
  - lxml
  - trimesh[easy]>=3.11.2
  - numpy
  - six
  - importlib-metadata ; python_version < '3.8'
  - pyglet<2 ; extra == 'full'
  - setuptools ; extra == 'testing'
  - pytest ; extra == 'testing'
  - pytest-cov ; extra == 'testing'
  requires_python: '>=3.7'
- kind: pypi
  name: zipp
  version: 3.18.1
  url: https://files.pythonhosted.org/packages/c2/0a/ba9d0ee9536d3ef73a3448e931776e658b36f128d344e175bc32b092a8bf/zipp-3.18.1-py3-none-any.whl
  sha256: 206f5a15f2af3dbaee80769fb7dc6f249695e940acca08dfb2a4769fe61e538b
  requires_dist:
  - sphinx>=3.5 ; extra == 'docs'
  - jaraco-packaging>=9.3 ; extra == 'docs'
  - rst-linker>=1.9 ; extra == 'docs'
  - furo ; extra == 'docs'
  - sphinx-lint ; extra == 'docs'
  - jaraco-tidelift>=1.4 ; extra == 'docs'
  - pytest>=6 ; extra == 'testing'
  - pytest-checkdocs>=2.4 ; extra == 'testing'
  - pytest-cov ; extra == 'testing'
  - pytest-enabler>=2.2 ; extra == 'testing'
  - pytest-ruff>=0.2.1 ; extra == 'testing'
  - jaraco-itertools ; extra == 'testing'
  - jaraco-functools ; extra == 'testing'
  - more-itertools ; extra == 'testing'
  - big-o ; extra == 'testing'
  - pytest-ignore-flaky ; extra == 'testing'
  - pytest-mypy ; platform_python_implementation != 'PyPy' and extra == 'testing'
  requires_python: '>=3.8'
- kind: conda
  name: zlib
  version: 1.2.13
  build: hd590300_5
  build_number: 5
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/zlib-1.2.13-hd590300_5.conda
  sha256: 9887a04d7e7cb14bd2b52fa01858f05a6d7f002c890f618d9fcd864adbfecb1b
  md5: 68c34ec6149623be41a1933ab996a209
  depends:
  - libgcc-ng >=12
  - libzlib 1.2.13 hd590300_5
  license: Zlib
  license_family: Other
  size: 92825
  timestamp: 1686575231103
- kind: conda
  name: zstd
  version: 1.5.6
  build: ha6fb4c9_0
  subdir: linux-64
  url: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.6-ha6fb4c9_0.conda
  sha256: c558b9cc01d9c1444031bd1ce4b9cff86f9085765f17627a6cd85fc623c8a02b
  md5: 4d056880988120e29d75bfff282e0f45
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<1.3.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 554846
  timestamp: 1714722996770
