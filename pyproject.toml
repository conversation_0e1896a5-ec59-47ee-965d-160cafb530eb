[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "nerfstudio"
version = "1.1.5"
description = "All-in-one repository for state-of-the-art NeRFs"
readme = "README.md"
license = { text="Apache 2.0"}
requires-python = ">=3.8.0"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Programming Language :: Python",
]
dependencies = [
    "appdirs>=1.4",
    "av>=9.2.0",
    "comet_ml>=3.33.8",
    "cryptography>=38",
    "tyro>=0.9.8",
    "gdown>=4.6.0",
    "ninja>=1.10",
    "h5py>=2.9.0",
    "imageio>=2.21.1",
    'importlib-metadata>=6.0.0; python_version < "3.10"',
    "ipywidgets>=7.6",
    "jaxtyping>=0.2.15",
    "jupyterlab>=3.3.4",
    "matplotlib>=3.6.0",
    "mediapy>=1.1.0",
    "msgpack>=1.0.4",
    "msgpack_numpy>=0.4.8",
    "nerfacc==0.5.2",
    "open3d>=0.16.0",
    "opencv-python-headless==*********",
    "Pillow>=10.3.0",
    "plotly>=5.7.0",
    "protobuf<=3.20.3,!=3.20.0",
    # TODO(1480) enable when pycolmap windows wheels are available
    # "pycolmap==0.3.0",
    # TODO(3461) and external issue cnr-isti-vclab/PyMeshLab/issues/398: Unrestrict Windows version when it isn't broken anymore.
    "pymeshlab>=2022.2.post2; platform_machine != 'arm64' and platform_machine != 'aarch64'",
    "pymeshlab<2023.12.post2; sys_platform == 'win32' and platform_machine != 'arm64' and platform_machine != 'aarch64'",
    "pyngrok>=5.1.0",
    "python-socketio>=5.7.1",
    "pyquaternion>=0.9.9",
    # TODO we can switch back to (non-new) rawpy if they start releasing arm64
    # wheels. https://github.com/letmaik/rawpy/issues/171#issuecomment-1572627747
    "rawpy>=0.18.1; platform_machine != 'arm64'",
    "newrawpy>=1.0.0b0; platform_machine == 'arm64'",
    "requests",
    "rich>=12.5.1",
    "scikit-image>=0.19.3",
    "splines==0.3.0",
    "tensorboard>=2.13.0",
    "torch>=1.13.1",
    "torchvision>=0.14.1",
    "torchmetrics[image]>=1.0.1",
    "typing_extensions>=4.4.0",
    "viser==0.2.7",
    "nuscenes-devkit>=1.1.1",
    "wandb>=0.13.3",
    "xatlas",
    "trimesh>=3.20.2",
    "timm==0.6.7",
    "gsplat==1.4.0",
    "pytorch-msssim",
    "pathos",
    "packaging",
    "fpsample",
    "tensorly"
]

[project.urls]
"Documentation" = "https://docs.nerf.studio"


[project.optional-dependencies]

# Generative related dependencies
gen = [
    "diffusers==0.16.1",
    "transformers==4.29.2",
    "accelerate==0.19.0",
    "bitsandbytes==0.39.0",
    "sentencepiece==0.1.99",
]


# Development packages
dev = [
    "pre-commit==3.3.2",
    "pytest==7.1.2",
    "pytest-xdist==2.5.0",
    "ruff>=0.6.1",
    "sshconf==0.2.5",
    "pycolmap>=0.3.0",  # NOTE: pycolmap==0.3.0 is not available on newer python versions
    "diffusers==0.16.1",
    "opencv-stubs==0.0.7",
    "transformers==4.29.2",
    "pyright==1.1.331",
    # NOTE: Disabling projectaria-tools because it doesn't have prebuilt windows wheels
    # Syntax comes from here: https://pip.pypa.io/en/stable/reference/requirement-specifiers/
    "projectaria-tools>=1.3.1; sys_platform != 'win32'",
    # pin torch to <=2.1 to fix https://github.com/pytorch/pytorch/issues/118736
    "torch>=1.13.1,<2.2",
    "awscli==1.33.18"
]

# Documentation related packages
docs = [
    "furo==2022.09.29",
    # Specifying ipython for https://github.com/ipython/ipython/issues/13845
    "ipython==8.6.0",
    "readthedocs-sphinx-search==0.1.2",
    "myst-nb==0.16.0",
    "nbconvert==7.2.5",
    "nbformat==5.9.2",
    "sphinx==5.2.1",
    "sphinxemoji==0.2.0",
    "sphinx-argparse==0.3.1",
    "sphinx-copybutton==0.5.0",
    "sphinx-design==0.2.0",
    "sphinxext-opengraph==0.6.3"
]

[project.scripts]
# Note, add entrypoint name to nerfstudio/scripts/completions/install.py to include CLI completion
ns-install-cli = "nerfstudio.scripts.completions.install:entrypoint"
ns-process-data = "nerfstudio.scripts.process_data:entrypoint"
ns-download-data = "nerfstudio.scripts.downloads.download_data:entrypoint"
ns-train = "nerfstudio.scripts.train:entrypoint"
ns-viewer = "nerfstudio.scripts.viewer.run_viewer:entrypoint"
ns-eval = "nerfstudio.scripts.eval:entrypoint"
ns-render = "nerfstudio.scripts.render:entrypoint"
ns-export = "nerfstudio.scripts.exporter:entrypoint"
ns-dev-test = "nerfstudio.scripts.github.run_actions:entrypoint"
ns-dev-sync-viser-message-defs = "nerfstudio.scripts.viewer.sync_viser_message_defs:entrypoint"

[tool.setuptools.packages.find]
include = ["nerfstudio*"]

[tool.setuptools.package-data]
"*" = ["*.cu", "*.json", "py.typed", "setup.bash", "setup.zsh"]

[tool.pytest.ini_options]
addopts = "-n=4 --jaxtyping-packages=nerfstudio --disable-warnings"
testpaths = [
    "tests",
]

[tool.pyright]
include = ["nerfstudio"]
exclude = ["**/node_modules",
    "**/__pycache__",
]
defineConstant = { DEBUG = true }

reportMissingImports = "warning"
reportMissingTypeStubs = false
reportPrivateImportUsage = false

pythonVersion = "3.8"
pythonPlatform = "Linux"

[tool.ruff]
line-length = 120
respect-gitignore = false
lint.select = [
    "E",  # pycodestyle errors.
    "F",  # Pyflakes rules.
    "I",  # isort formatting.
    "PLC",  # Pylint convention warnings.
    "PLE",  # Pylint errors.
    "PLR",  # Pylint refactor recommendations.
    "PLW",  # Pylint warnings.
    "NPY201" # NumPY 2.0 migration https://numpy.org/devdocs/numpy_2_0_migration_guide.html#ruff-plugin
]
lint.ignore = [
    "E501",  # Line too long.
    "F722",  # Forward annotation false positive from jaxtyping. Should be caught by pyright.
    "F821",  # Forward annotation false positive from jaxtyping. Should be caught by pyright.
    "PLR2004",  # Magic value used in comparison.
    "PLR0915",  # Too many statements.
    "PLR0913",  # Too many arguments.
    "PLC0414",  # Import alias does not rename variable. (this is used for exporting names)
    "PLC1901",  # Use falsey strings.
    "PLR5501",  # Use `elif` instead of `else if`.
    "PLR0911",  # Too many return statements.
    "PLR0912",  # Too many branches.
    "PLW0603",  # Globa statement updates are discouraged.
    "PLW2901",  # For loop variable overwritten.
    "PLR1730",  # Replace if statement with min/max
    "PLC0206",  # Extracting value from dictionary without calling `.items()`
]

[tool.ruff.lint.isort]
combine-as-imports = true
known-first-party = ["nerfstudio"]
split-on-trailing-comma = false
